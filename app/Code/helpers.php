<?php

use App\Code\Ajax;
use App\Code\External\Api\SalesforceMarketingApi;
use App\Code\Misc\Csvimport;
use App\Http\Models\Admins;
use App\Http\Models\Api\Address;
use App\Http\Models\Api\Country;
use App\Http\Models\Api\Feed;
use App\Http\Models\Api\Text;
use App\Http\Models\Api\User;
use chillerlan\QRCode\QRCode;
use chillerlan\QRCode\QROptions;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Tymon\JWTAuth\Providers\JWT\Lcobucci;

function toApiSingleResult($data = [], $meta = []){
    /* @var Illuminate\Support\Collection $collection */
    $returnData         = [];
    $returnData["data"] = $data;
    $returnData["meta"] = $meta;
    return $returnData;
}

function toApiCollectionResult($collection = [], $totalAmount = 0, $offset = 0, $limit = 0, $fillMeta = true){
    /* @var Illuminate\Support\Collection $collection */
    $returnData         = [];
    $returnData["data"] = is_array($collection) ? $collection : $collection->all();
    $returnData["meta"] = [];
    if($fillMeta){
        $returnData["meta"]["offset"]        = $offset;
        $returnData["meta"]["limit"]         = $limit;
        $returnData["meta"]["results"]       = count($returnData["data"]);
        $returnData["meta"]["total_results"] = $totalAmount;
    }
    return $returnData;
}

function getApiPaginationData(Illuminate\Http\Request $request){
    // Sanitize pagination input
    $offset = @intval($request->input("offset"));
    $offset = $offset < 0 ? 0 : $offset;
    $offset = $offset > PHP_INT_MAX ? PHP_INT_MAX : $offset;
    $limit  = @intval($request->input("limit"));
    $limit  = $limit <= 0 ? 50 : $limit;
    $limit  = $limit > 2000 ? 2000 : $limit;
    $limit  = isFrontendUser() ? 10000 : $limit;
    return ["offset" => $offset, "limit" => $limit];
}

function fixVoucherData($vouchers = []){
    $outVouchers = [];

    foreach($vouchers as $voucher){
        $voucher["validFrom"]   = date("Y-m-d H:i:s", @strtotime($voucher["validFrom"] ?? ""));
        $voucher["validUntil"]  = date("Y-m-d H:i:s", @strtotime($voucher["validUntil"] ?? ""));
        $voucher["lastChanged"] = date("Y-m-d H:i:s", @strtotime($voucher["lastChanged"] ?? ""));

        $outVouchers[] = $voucher;
    }

    return $outVouchers;
}

function getBrandsUploadDir(){
    return public_path("uploads" . DIRECTORY_SEPARATOR . "brands") . DIRECTORY_SEPARATOR;
}

function getFeedsUploadDir(){
    return public_path("uploads" . DIRECTORY_SEPARATOR . "feeds") . DIRECTORY_SEPARATOR;
}

function getBrandsUploadUrl(){
    return url("public/uploads/brands") . "/";
}

function getFeedsUploadUrl(){
    return url("public/uploads/feeds") . "/";
}

function toSingleLine($string = ""){
    return trim(preg_replace('/\s+/', ' ', $string));
}

function AESdecypt($col, $colAs = true){
    $MYSQL_ENC_KEY = config("app.mysql_enc_key");
    $select        = "AES_DECRYPT(" . $col . ", '" . $MYSQL_ENC_KEY . "')";
    if($colAs) $select .= " as " . $col;
    return $select;
}

function AESdecypts($cols)  {

	$select = "";
	foreach($cols as $col)  {
		$select .= ", ".AESdecypt($col);
	}

	return $select;
}

function find_match($string1, $string2, $idealDiffCount = 1){
    $diifernetCount = 0;
    for($i = 0; $i < strlen($string2); $i++) // start loop
        if(!isset($string1[$i]) || $string1[$i] != $string2[$i]) // done match of character at exact position in both string
            $diifernetCount++;
    if($diifernetCount > $idealDiffCount) return false;
    else return true;
}

function random_string($length = 10){
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $randstring = '';
    for($i = 0; $i < $length; $i++) $randstring .= $characters[rand(0, (strlen($characters) - 1))];
    return $randstring;
}

function shortClassMethod($classMethodStr = ""){
    return substr(strrchr($classMethodStr, "\\"), 1);
}

function removeOldTmpFiles(){
    $files = glob(storage_path("tmp") . DIRECTORY_SEPARATOR . "uploads" . DIRECTORY_SEPARATOR . "*");
    $now   = time();
    foreach($files as $file) if(is_file($file)) // 1 day
        if($now - filemtime($file) >= 60 * 60 * 24 * 1) @unlink($file);
    $files = glob(storage_path("tmp") . DIRECTORY_SEPARATOR . "invoice" . DIRECTORY_SEPARATOR . "*");
    $now   = time();
    foreach($files as $file) if(is_file($file)) // 1 day
        if($now - filemtime($file) >= 60 * 60 * 24 * 1) @unlink($file);
    $files = glob(public_path("uploads") . DIRECTORY_SEPARATOR . "tmp" . DIRECTORY_SEPARATOR . "*");
    $now   = time();
    foreach($files as $file) if(is_file($file)) // 1 day
        if($now - filemtime($file) >= 60 * 60 * 24 * 1) @unlink($file);
}

function frontendDebugLog(Ajax $ajax, $message = ""){
    if(devModeActive()) $ajax->action("log", ["value" => json_encode($message)]);
}

function devModeActive(){
    $serverName = $_SERVER["SERVER_NAME"] ?? "";
    return config("app.debug") && in_array($serverName, ["localhost", "dev21.contentcreators.at"]);
}

function appendFlashSessionChangelog($key = "", $value = ""){
    $changesList   = session()->has($key) ?
        session()->get($key) : [];
    $changesList   = $changesList ?: array();
    $changesList[] = $value;
    session()->flash($key, $changesList);
}

function getFlashSessionChangelog($key = ""){
    $changesList = session()->has($key) ?
        session()->get($key) : [];
    $changesList = $changesList ?: array();
    return $changesList;
}

function isLocalhost(){
    $serverName = $_SERVER["SERVER_NAME"] ?? "";
    return in_array($serverName, ["localhost"]);
}

function isDevhost(){
    $serverName = $_SERVER["SERVER_NAME"] ?? "";
    return in_array($serverName, ["dev21.contentcreators.at"]);
}

function isDevEnv(){
    return isLocalhost() || isDevhost();
}

function genUniqueToken(){
    return slugify_string_keep_case(base64_encode(uniqid(rand(), true)));
}

function getActiveLanguageName(){
    return app(\App\Code\Api\RequestLanguage::class)->getActiveLanguageCode();
}

function getSwitchLanguages(){
    return app(\App\Code\Api\RequestLanguage::class)->getSwitchLanguages();
}

function getActiveLanguageId(){
    return app(\App\Code\Api\RequestLanguage::class)->getActiveLanguageId();
}

function buildSettingsCache($rebuild = false){
    if(!$rebuild && Cache::store('array')->has('settingsCache')) return Cache::store('array')->get('settingsCache', []);
    $rows        = DB::table('settings')->select("label", "value")->get();
    $settingsMap = [];
    foreach($rows as $row) $settingsMap[$row->label] = $row->value;
    $expiresAt = Carbon::now()->addMinutes(10);
    Cache::store('array')->put('settingsCache', $settingsMap, $expiresAt);
    return $settingsMap;
}

function settings($label, $value = '', $data = []){
    $settingsMap = buildSettingsCache();
    if(isset($settingsMap[$label])) $retValue = $settingsMap[$label];
    else{
        $value = sanitizeHTMLStr($value);
        $data  = array(
            "label"      => sanitizeStr($label),
            "value"      => $value,
            "created_at" => date('Y-m-d H:i:s', time()),
            "updated_at" => date('Y-m-d H:i:s', time()),
        );
        DB::table('settings')->insert($data);
        $retValue = $value;
        buildSettingsCache(true);
    }

    // Add whitespace at every line end
    $text = trim(implode("\n", array_map(function ($text){
        return $text . " ";
    }, array_map('trim', explode("\n", $retValue)))));

    // Replace variable placeholders if data present
    if($data) foreach($data as $key => $value)
        $text = str_ireplace("%" . $key . "%", $value, $text);

    // Ret
    return $text;
}

function save_setting($label, $value = ''){
    $row = DB::table('settings')->where('label', sanitizeStr($label))->first();
    if($row){
        $data = array(
            "value"      => $value,
            "updated_at" => date('Y-m-d H:i:s', time()),
        );
        DB::table('settings')->where('label', sanitizeStr($label))->update($data);
    }
    else{
        $value = sanitizeHTMLStr($value);
        $data  = array(
            "label"      => sanitizeStr($label),
            "value"      => $value,
            "created_at" => date('Y-m-d H:i:s', time()),
            "updated_at" => date('Y-m-d H:i:s', time()),
        );
        DB::table('settings')->insert($data);
    }
    buildSettingsCache(true);
    return $value;
}

function timeToUtc($timestamp){
    return strtotime(gmdate('d.m.Y H:i', $timestamp));
}

function isJson($value = null){
    if(is_array($value)) return false;
    json_decode($value);
    return (json_last_error() === JSON_ERROR_NONE);
}

function isSerialized($data, $strict = true){
    // If it isn't a string, it isn't serialized.
    if(!is_string($data)){
        return false;
    }
    $data = trim($data);
    if('N;' === $data){
        return true;
    }
    if(strlen($data) < 4){
        return false;
    }
    if(':' !== $data[1]){
        return false;
    }
    if($strict){
        $lastc = substr($data, -1);
        if(';' !== $lastc && '}' !== $lastc){
            return false;
        }
    }
    else{
        $semicolon = strpos($data, ';');
        $brace     = strpos($data, '}');
        // Either ; or } must exist.
        if(false === $semicolon && false === $brace){
            return false;
        }
        // But neither must be in the first X characters.
        if(false !== $semicolon && $semicolon < 3){
            return false;
        }
        if(false !== $brace && $brace < 4){
            return false;
        }
    }
    $token = $data[0];
    switch($token){
        case 's':
            if($strict){
                if('"' !== substr($data, -2, 1)){
                    return false;
                }
            }
            elseif(false === strpos($data, '"')){
                return false;
            }
        // Or else fall through.
        case 'a':
        case 'O':
            return (bool)preg_match("/^{$token}:[0-9]+:/s", $data);
        case 'b':
        case 'i':
        case 'd':
            $end = $strict ? '$' : '';
            return (bool)preg_match("/^{$token}:[0-9.E+-]+;$end/", $data);
    }
    return false;
}

function tryElse($closure, $default = null){
    $result = $default;
    if(is_callable($closure)){
        try{
            $result = $closure();
        }
        catch(Exception $e){
            Log::channel("api_err")->error(sprintf("%s::tryElse -> %s file %s @ line %s trace %s", debug_backtrace()[1]['function'], $e->getMessage(), $e->getFile(), $e->getLine(), $e->getTraceAsString()));
            return $default;
        }
    }
    return $result;
}

function sanitizeFileName($text = ""){
    $callingFunction = mb_ereg_replace("([^\w\s\d\-_~,;\[\]\(\).])", '', $text);
    return mb_ereg_replace("([\.]{2,})", '', $callingFunction);
}

function ensureDir($dirPath = ""){
    if(!file_exists($dirPath) && !mkdir($dirPath) && !is_dir($dirPath)){
        throw new \RuntimeException(sprintf('Directory "%s" was not created', $dirPath));
    }
}

function removePreDialFromData($data = []){
    if(isset($data["mobile"])){
        $regex        = '/\+[0-9]{2}[\s]*/i';
        $mobileNumber = @$data["mobile"];
        preg_match($regex, $mobileNumber, $matches, PREG_OFFSET_CAPTURE);
        $mobilePrefix   = @$matches[0][0];
        $mobileNoPrefix = preg_replace($regex, "", $mobileNumber);
        $finalMobile    = @trim($mobilePrefix) . @trim($mobileNoPrefix);
        $data["mobile"] = $finalMobile;
    }

    return $data;
}

function genUserEmailConfirmCode(){
    $i           = 0;
    $confirmCode = \Illuminate\Support\Str::random(32);
    while(++$i < 100){
        if(!User::where("email_confirm_code", $confirmCode)->first()) break;
    }
    return $confirmCode;
}

function genUserResetCode(){
    $i           = 0;
    $confirmCode = \Illuminate\Support\Str::random(32);
    while(++$i < 100){
        if(!User::where("reset_token", $confirmCode)->first()) break;
    }
    return $confirmCode;
}

function correctDate($date, $format = 'Y-m-d'){
    $d = DateTime::createFromFormat($format, $date);
    // The Y ( 4 digits year ) returns TRUE for any integer with any number of digits so changing the comparison from == to === fixes the issue.
    return $d && $d->format($format) === $date;
}

function correctDateTime($dateTime, $format = 'Y-m-d H:i:s'){
    $d = DateTime::createFromFormat($format, $dateTime);
    // The Y ( 4 digits year ) returns TRUE for any integer with any number of digits so changing the comparison from == to === fixes the issue.
    return $d && $d->format($format) === $dateTime;
}

function checkmydate($date){
    $date     = date("Y-m-d", @strtotime($date));
    $tempDate = explode('-', $date);
    return checkdate($tempDate[1], $tempDate[2], $tempDate[0]);
}

function debugLogExport($data = false){
    if(config("app.debug")) Log::debug(sprintf("%s() - %s", __METHOD__, var_export($data, true)));
}

function sanitizeArr($list = []){
    return array_map(function ($item){
        return sanitizeStrFull($item);
    }, $list);
}

function sanitizeStrFull($text = ''){
    return sanitizeStr(sanitizeHTMLStr($text));
}

function sanitizeHTMLStr($text = ''){
    return trim(strip_tags($text, '<p><a><span><h1><h2><h3><h4><h5><h6><br><strong><sup><sub><i><small><s><u><ul><li><ol>'));
}

function sanitizeMarkupHTML($text = ''){
    return trim(str_ireplace("<br>", "\\", $text));
    //return trim(strip_tags($text, '<br><b>'));
}

function sanitizeStr($text = ""){
    return filter_var($text, FILTER_SANITIZE_STRING, FILTER_FLAG_NO_ENCODE_QUOTES);
}

function M($M, $key, $default = false){
    if(!is_array($M) || !key_exists($key, $M) || $M[$key] == '') return $default;
    return $M[$key];
}

function M_sanitize($M, $key, $default = false){
    return sanitizeStr(M($M, $key, $default));
}

function slugify_string($string){
    $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $string)));
    return $slug;
}

function slugify_string_keep_case($string){
    $slug = trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $string));
    return $slug;
}

function strExists($data = [], $key = ""){
    return isset($data[$key]) && is_string($data[$key]);
}

function itemExists($data = [], $key = ""){
    return isset($data[$key]);
}

function mobileAppText($key = "", $default = "", $data = []){
    return Text::getText($key, $default, Text::TEXT_GROUP_MOBILE_APP, Text::TEXT_TYPE_DEFAULT, false, $data);
}

function emailText($key = "", $default = "", $data = []){
    return Text::getText($key, $default, Text::TEXT_GROUP_EMAILS, Text::TEXT_TYPE_DEFAULT, false, $data);
}

function apiText($key = "", $default = "", $data = []){
    return Text::getText($key, $default, Text::TEXT_GROUP_API, Text::TEXT_TYPE_DEFAULT, false, $data);
}

function posAppText($key = "", $default = "", $data = []){
    return Text::getText($key, $default, Text::TEXT_GROUP_POS_APP, Text::TEXT_TYPE_DEFAULT, false, $data);
}

function webText($key = "", $default = "", $data = []){
    return Text::getText($key, $default, Text::TEXT_GROUP_DEFAULT, Text::TEXT_TYPE_DEFAULT, false, $data);
}

function containsOnlyAlphabet($text = ""){
    return !preg_match('/[^A-Za-z\x{00C0}-\x{00D6}\x{00D8}-\x{00f6}\x{00f8}-\x{00ff}\s\.\-\,\'\´\`\/\w-]/iu', $text);
}

function containsOnlyNumbers($text = ""){
    return !preg_match('/[^\s_\-0-9+]/i', $text);
}

function containsAlphanumbericals($text = ""){
    return !preg_match('/[^A-Za-z\x{00C0}-\x{00D6}\x{00D8}-\x{00f6}\x{00f8}-\x{00ff}\s\.\-\,\'\´\`\/0-9\w-]/iu', $text);
}

function getSalesTimestamp($dateText = ""){
    return \Carbon\Carbon::createFromFormat("Y-m-d", $dateText)->getTimestamp();
}

function publish_options(){

    return array(
        "DRAFT"   => 0,
        "PUBLISH" => 1,
    );
}

function visibility_options(){

    return array(
        "Logged in"     => "1",
        "Not logged in" => "0",
    );
}

function salutation_options(){

    return array(
        "Mr"    => "Mr",
        "MS"    => "MS",
        "INTER" => "INTER",
    );
}

function gender_options(){

    return array(
        "Male"   => "m",
        "Female" => "f",
        "Inter"  => "d",
    );
}

function feed_size_options(){
    return array(
        "LARGE" => Feed::FEED_SIZE_LARGE,
        "SMALL" => Feed::FEED_SIZE_SMALL,
    );
}

function outCSS($collectionName = ""){ // @ see config/assets.php
    Assets::reset();
    Assets::add($collectionName);
    return Assets::css(function ($assets){
        $output = '';
        foreach($assets as $asset){
            $url    = url('public') . "/" . $asset;
            $url    = str_replace("css/../", "", $url);
            $output .= '<link rel="stylesheet" href="' . $url . '"' . " />\n";
        }
        return $output;
    });
}

function outJS($collectionName = ""){ // @ see config/assets.php
    Assets::reset();
    Assets::add($collectionName);
    return Assets::js(function ($assets){
        $output = '';
        foreach($assets as $asset){
            $url    = url('public') . "/" . $asset;
            $url    = str_replace("js/../", "", $url);
            $output .= '<script nonce="' . csp_nonce() . '" src="' . $url . '"' . "></script>\n";
        }
        return $output;
    });
}

function validPhoneNumber($number = ""){
    try{
        $phoneUtil   = \libphonenumber\PhoneNumberUtil::getInstance();
        $numberProto = $phoneUtil->parse($number);
        return $phoneUtil->isValidNumber($numberProto);
    }
    catch(Exception $e){
        Log::error(var_export(["number" => $number, "exception" => $e->getMessage()], true));
        return false;
    }
}

function getPhoneCountryByCode($code = ""){
    $phoneMap = getCountryPhoneCodeMap();
    foreach($phoneMap as $countryCode => $phoneEntry){
        $flagFile = @strtolower($countryCode) . ".png";
        $fullPath = public_path("gfx" . DIRECTORY_SEPARATOR . "flags") . DIRECTORY_SEPARATOR . $flagFile;
        if(!file_exists($fullPath))
            continue;
        $phoneEntry["flag_file"] = strtolower($countryCode) . ".png";
        if($phoneEntry["code"] === $code) return $phoneEntry;
    }
    return [
        "code"      => "43",
        "flag_file" => "43" . ".png",
        "name"      => "Austria",
    ];
}

function getPhoneCountries(){
    $phoneMap   = getCountryPhoneCodeMap();
    $outEntries = [];
    foreach($phoneMap as $countryCode => $phoneEntry){
        $flagFile = @strtolower($countryCode) . ".png";
        $fullPath = public_path("gfx" . DIRECTORY_SEPARATOR . "flags") . DIRECTORY_SEPARATOR . $flagFile;
        if(!file_exists($fullPath))
            continue;

        $outEntries[] = [
            "code"      => $phoneEntry["code"],
            "flag_file" => @strtolower($countryCode) . ".png",
            "name"      => @$phoneEntry["name"]
        ];
    }

    usort($outEntries, function ($a, $b){
        return strcmp(@$a["name"], @$b["name"]);
    });

    return $outEntries;
}

function getCountryPhoneCodeMap(){
    return array(
        'AX' => array('name' => 'ALAND ISLANDS', 'code' => '358'),
        'BQ' => array('name' => 'BONAIRE, SINT EUSTATIUS AND SABA', 'code' => '599'),
        'BV' => array('name' => 'BOUVET ISLAND', 'code' => '55'),
        'IO' => array('name' => 'BRITISH INDIAN OCEAN TERRITORY', 'code' => '246'),
        'CW' => array('name' => 'CURAÇAO', 'code' => '599'),
        'GF' => array('name' => 'FRENCH GUIANA', 'code' => '594'),
        'TF' => array('name' => 'FRENCH SOUTHERN TERRITORIES', 'code' => '262'),
        'GP' => array('name' => 'GUADELOUPE', 'code' => '590'),
        'GG' => array('name' => 'GUERNSEY', 'code' => '44'),
        'HM' => array('name' => 'HEARD ISLAND AND MCDONALD ISLANDS', 'code' => '672'),
        'JE' => array('name' => 'JERSEY', 'code' => '44'),
        'MQ' => array('name' => 'MARTINIQUE', 'code' => '596'),
        'NF' => array('name' => 'NORFOLK ISLAND', 'code' => '672'),
        'PS' => array('name' => 'PALESTINIAN TERRITORY, OCCUPIED', 'code' => '970'),
        'RE' => array('name' => 'REUNION', 'code' => '262'),
        'SX' => array('name' => 'SINT MAARTEN (DUTCH PART)', 'code' => '599'),
        'GS' => array('name' => 'SOUTH GEORGIA AND THE SOUTH SANDWICH ISLANDS', 'code' => '500'),
        'SS' => array('name' => 'SOUTH SUDAN', 'code' => '211'),
        'EH' => array('name' => 'WESTERN SAHARA', 'code' => '212'),
        'SJ' => array('name' => 'Svalbard and Jan Mayen', 'code' => '47'),
        'EN' => array('name' => 'UNITED STATES', 'code' => '1'),

        'AD' => array('name' => 'ANDORRA', 'code' => '376'),
        'AE' => array('name' => 'UNITED ARAB EMIRATES', 'code' => '971'),
        'AF' => array('name' => 'AFGHANISTAN', 'code' => '93'),
        'AG' => array('name' => 'ANTIGUA AND BARBUDA', 'code' => '1268'),
        'AI' => array('name' => 'ANGUILLA', 'code' => '1264'),
        'AL' => array('name' => 'ALBANIA', 'code' => '355'),
        'AM' => array('name' => 'ARMENIA', 'code' => '374'),
        'AN' => array('name' => 'NETHERLANDS ANTILLES', 'code' => '599'),
        'AO' => array('name' => 'ANGOLA', 'code' => '244'),
        'AQ' => array('name' => 'ANTARCTICA', 'code' => '672'),
        'AR' => array('name' => 'ARGENTINA', 'code' => '54'),
        'AS' => array('name' => 'AMERICAN SAMOA', 'code' => '1684'),
        'AT' => array('name' => 'AUSTRIA', 'code' => '43'),
        'AU' => array('name' => 'AUSTRALIA', 'code' => '61'),
        'AW' => array('name' => 'ARUBA', 'code' => '297'),
        'AZ' => array('name' => 'AZERBAIJAN', 'code' => '994'),
        'BA' => array('name' => 'BOSNIA AND HERZEGOVINA', 'code' => '387'),
        'BB' => array('name' => 'BARBADOS', 'code' => '1246'),
        'BD' => array('name' => 'BANGLADESH', 'code' => '880'),
        'BE' => array('name' => 'BELGIUM', 'code' => '32'),
        'BF' => array('name' => 'BURKINA FASO', 'code' => '226'),
        'BG' => array('name' => 'BULGARIA', 'code' => '359'),
        'BH' => array('name' => 'BAHRAIN', 'code' => '973'),
        'BI' => array('name' => 'BURUNDI', 'code' => '257'),
        'BJ' => array('name' => 'BENIN', 'code' => '229'),
        'BL' => array('name' => 'SAINT BARTHELEMY', 'code' => '590'),
        'BM' => array('name' => 'BERMUDA', 'code' => '1441'),
        'BN' => array('name' => 'BRUNEI DARUSSALAM', 'code' => '673'),
        'BO' => array('name' => 'BOLIVIA', 'code' => '591'),
        'BR' => array('name' => 'BRAZIL', 'code' => '55'),
        'BS' => array('name' => 'BAHAMAS', 'code' => '1242'),
        'BT' => array('name' => 'BHUTAN', 'code' => '975'),
        'BW' => array('name' => 'BOTSWANA', 'code' => '267'),
        'BY' => array('name' => 'BELARUS', 'code' => '375'),
        'BZ' => array('name' => 'BELIZE', 'code' => '501'),
        'CA' => array('name' => 'CANADA', 'code' => '1'),
        'CC' => array('name' => 'COCOS (KEELING) ISLANDS', 'code' => '61'),
        'CD' => array('name' => 'CONGO, THE DEMOCRATIC REPUBLIC OF THE', 'code' => '243'),
        'CF' => array('name' => 'CENTRAL AFRICAN REPUBLIC', 'code' => '236'),
        'CG' => array('name' => 'CONGO', 'code' => '242'),
        'CH' => array('name' => 'SWITZERLAND', 'code' => '41'),
        'CI' => array('name' => 'COTE D IVOIRE', 'code' => '225'),
        'CK' => array('name' => 'COOK ISLANDS', 'code' => '682'),
        'CL' => array('name' => 'CHILE', 'code' => '56'),
        'CM' => array('name' => 'CAMEROON', 'code' => '237'),
        'CN' => array('name' => 'CHINA', 'code' => '86'),
        'CO' => array('name' => 'COLOMBIA', 'code' => '57'),
        'CR' => array('name' => 'COSTA RICA', 'code' => '506'),
        'CU' => array('name' => 'CUBA', 'code' => '53'),
        'CV' => array('name' => 'CAPE VERDE', 'code' => '238'),
        'CX' => array('name' => 'CHRISTMAS ISLAND', 'code' => '61'),
        'CY' => array('name' => 'CYPRUS', 'code' => '357'),
        'CZ' => array('name' => 'CZECH REPUBLIC', 'code' => '420'),
        'DE' => array('name' => 'GERMANY', 'code' => '49'),
        'DJ' => array('name' => 'DJIBOUTI', 'code' => '253'),
        'DK' => array('name' => 'DENMARK', 'code' => '45'),
        'DM' => array('name' => 'DOMINICA', 'code' => '1767'),
        'DO' => array('name' => 'DOMINICAN REPUBLIC', 'code' => '1809'),
        'DZ' => array('name' => 'ALGERIA', 'code' => '213'),
        'EC' => array('name' => 'ECUADOR', 'code' => '593'),
        'EE' => array('name' => 'ESTONIA', 'code' => '372'),
        'EG' => array('name' => 'EGYPT', 'code' => '20'),
        'ER' => array('name' => 'ERITREA', 'code' => '291'),
        'ES' => array('name' => 'SPAIN', 'code' => '34'),
        'ET' => array('name' => 'ETHIOPIA', 'code' => '251'),
        'FI' => array('name' => 'FINLAND', 'code' => '358'),
        'FJ' => array('name' => 'FIJI', 'code' => '679'),
        'FK' => array('name' => 'FALKLAND ISLANDS (MALVINAS)', 'code' => '500'),
        'FM' => array('name' => 'MICRONESIA, FEDERATED STATES OF', 'code' => '691'),
        'FO' => array('name' => 'FAROE ISLANDS', 'code' => '298'),
        'FR' => array('name' => 'FRANCE', 'code' => '33'),
        'GA' => array('name' => 'GABON', 'code' => '241'),
        'GB' => array('name' => 'UNITED KINGDOM', 'code' => '44'),
        'GD' => array('name' => 'GRENADA', 'code' => '1473'),
        'GE' => array('name' => 'GEORGIA', 'code' => '995'),
        'GH' => array('name' => 'GHANA', 'code' => '233'),
        'GI' => array('name' => 'GIBRALTAR', 'code' => '350'),
        'GL' => array('name' => 'GREENLAND', 'code' => '299'),
        'GM' => array('name' => 'GAMBIA', 'code' => '220'),
        'GN' => array('name' => 'GUINEA', 'code' => '224'),
        'GQ' => array('name' => 'EQUATORIAL GUINEA', 'code' => '240'),
        'GR' => array('name' => 'GREECE', 'code' => '30'),
        'GT' => array('name' => 'GUATEMALA', 'code' => '502'),
        'GU' => array('name' => 'GUAM', 'code' => '1671'),
        'GW' => array('name' => 'GUINEA-BISSAU', 'code' => '245'),
        'GY' => array('name' => 'GUYANA', 'code' => '592'),
        'HK' => array('name' => 'HONG KONG', 'code' => '852'),
        'HN' => array('name' => 'HONDURAS', 'code' => '504'),
        'HR' => array('name' => 'CROATIA', 'code' => '385'),
        'HT' => array('name' => 'HAITI', 'code' => '509'),
        'HU' => array('name' => 'HUNGARY', 'code' => '36'),
        'ID' => array('name' => 'INDONESIA', 'code' => '62'),
        'IE' => array('name' => 'IRELAND', 'code' => '353'),
        'IL' => array('name' => 'ISRAEL', 'code' => '972'),
        'IM' => array('name' => 'ISLE OF MAN', 'code' => '44'),
        'IN' => array('name' => 'INDIA', 'code' => '91'),
        'IQ' => array('name' => 'IRAQ', 'code' => '964'),
        'IR' => array('name' => 'IRAN, ISLAMIC REPUBLIC OF', 'code' => '98'),
        'IS' => array('name' => 'ICELAND', 'code' => '354'),
        'IT' => array('name' => 'ITALY', 'code' => '39'),
        'JM' => array('name' => 'JAMAICA', 'code' => '1876'),
        'JO' => array('name' => 'JORDAN', 'code' => '962'),
        'JP' => array('name' => 'JAPAN', 'code' => '81'),
        'KE' => array('name' => 'KENYA', 'code' => '254'),
        'KG' => array('name' => 'KYRGYZSTAN', 'code' => '996'),
        'KH' => array('name' => 'CAMBODIA', 'code' => '855'),
        'KI' => array('name' => 'KIRIBATI', 'code' => '686'),
        'KM' => array('name' => 'COMOROS', 'code' => '269'),
        'KN' => array('name' => 'SAINT KITTS AND NEVIS', 'code' => '1869'),
        'KP' => array('name' => 'KOREA DEMOCRATIC PEOPLES REPUBLIC OF', 'code' => '850'),
        'KR' => array('name' => 'KOREA REPUBLIC OF', 'code' => '82'),
        'KW' => array('name' => 'KUWAIT', 'code' => '965'),
        'KY' => array('name' => 'CAYMAN ISLANDS', 'code' => '1345'),
        'KZ' => array('name' => 'KAZAKSTAN', 'code' => '7'),
        'LA' => array('name' => 'LAO PEOPLES DEMOCRATIC REPUBLIC', 'code' => '856'),
        'LB' => array('name' => 'LEBANON', 'code' => '961'),
        'LC' => array('name' => 'SAINT LUCIA', 'code' => '1758'),
        'LI' => array('name' => 'LIECHTENSTEIN', 'code' => '423'),
        'LK' => array('name' => 'SRI LANKA', 'code' => '94'),
        'LR' => array('name' => 'LIBERIA', 'code' => '231'),
        'LS' => array('name' => 'LESOTHO', 'code' => '266'),
        'LT' => array('name' => 'LITHUANIA', 'code' => '370'),
        'LU' => array('name' => 'LUXEMBOURG', 'code' => '352'),
        'LV' => array('name' => 'LATVIA', 'code' => '371'),
        'LY' => array('name' => 'LIBYAN ARAB JAMAHIRIYA', 'code' => '218'),
        'MA' => array('name' => 'MOROCCO', 'code' => '212'),
        'MC' => array('name' => 'MONACO', 'code' => '377'),
        'MD' => array('name' => 'MOLDOVA, REPUBLIC OF', 'code' => '373'),
        'ME' => array('name' => 'MONTENEGRO', 'code' => '382'),
        'MF' => array('name' => 'SAINT MARTIN', 'code' => '1599'),
        'MG' => array('name' => 'MADAGASCAR', 'code' => '261'),
        'MH' => array('name' => 'MARSHALL ISLANDS', 'code' => '692'),
        'MK' => array('name' => 'MACEDONIA, THE FORMER YUGOSLAV REPUBLIC OF', 'code' => '389'),
        'ML' => array('name' => 'MALI', 'code' => '223'),
        'MM' => array('name' => 'MYANMAR', 'code' => '95'),
        'MN' => array('name' => 'MONGOLIA', 'code' => '976'),
        'MO' => array('name' => 'MACAU', 'code' => '853'),
        'MP' => array('name' => 'NORTHERN MARIANA ISLANDS', 'code' => '1670'),
        'MR' => array('name' => 'MAURITANIA', 'code' => '222'),
        'MS' => array('name' => 'MONTSERRAT', 'code' => '1664'),
        'MT' => array('name' => 'MALTA', 'code' => '356'),
        'MU' => array('name' => 'MAURITIUS', 'code' => '230'),
        'MV' => array('name' => 'MALDIVES', 'code' => '960'),
        'MW' => array('name' => 'MALAWI', 'code' => '265'),
        'MX' => array('name' => 'MEXICO', 'code' => '52'),
        'MY' => array('name' => 'MALAYSIA', 'code' => '60'),
        'MZ' => array('name' => 'MOZAMBIQUE', 'code' => '258'),
        'NA' => array('name' => 'NAMIBIA', 'code' => '264'),
        'NC' => array('name' => 'NEW CALEDONIA', 'code' => '687'),
        'NE' => array('name' => 'NIGER', 'code' => '227'),
        'NG' => array('name' => 'NIGERIA', 'code' => '234'),
        'NI' => array('name' => 'NICARAGUA', 'code' => '505'),
        'NL' => array('name' => 'NETHERLANDS', 'code' => '31'),
        'NO' => array('name' => 'NORWAY', 'code' => '47'),
        'NP' => array('name' => 'NEPAL', 'code' => '977'),
        'NR' => array('name' => 'NAURU', 'code' => '674'),
        'NU' => array('name' => 'NIUE', 'code' => '683'),
        'NZ' => array('name' => 'NEW ZEALAND', 'code' => '64'),
        'OM' => array('name' => 'OMAN', 'code' => '968'),
        'PA' => array('name' => 'PANAMA', 'code' => '507'),
        'PE' => array('name' => 'PERU', 'code' => '51'),
        'PF' => array('name' => 'FRENCH POLYNESIA', 'code' => '689'),
        'PG' => array('name' => 'PAPUA NEW GUINEA', 'code' => '675'),
        'PH' => array('name' => 'PHILIPPINES', 'code' => '63'),
        'PK' => array('name' => 'PAKISTAN', 'code' => '92'),
        'PL' => array('name' => 'POLAND', 'code' => '48'),
        'PM' => array('name' => 'SAINT PIERRE AND MIQUELON', 'code' => '508'),
        'PN' => array('name' => 'PITCAIRN', 'code' => '870'),
        'PR' => array('name' => 'PUERTO RICO', 'code' => '1'),
        'PT' => array('name' => 'PORTUGAL', 'code' => '351'),
        'PW' => array('name' => 'PALAU', 'code' => '680'),
        'PY' => array('name' => 'PARAGUAY', 'code' => '595'),
        'QA' => array('name' => 'QATAR', 'code' => '974'),
        'RO' => array('name' => 'ROMANIA', 'code' => '40'),
        'RS' => array('name' => 'SERBIA', 'code' => '381'),
        'RU' => array('name' => 'RUSSIAN FEDERATION', 'code' => '7'),
        'RW' => array('name' => 'RWANDA', 'code' => '250'),
        'SA' => array('name' => 'SAUDI ARABIA', 'code' => '966'),
        'SB' => array('name' => 'SOLOMON ISLANDS', 'code' => '677'),
        'SC' => array('name' => 'SEYCHELLES', 'code' => '248'),
        'SD' => array('name' => 'SUDAN', 'code' => '249'),
        'SE' => array('name' => 'SWEDEN', 'code' => '46'),
        'SG' => array('name' => 'SINGAPORE', 'code' => '65'),
        'SH' => array('name' => 'SAINT HELENA', 'code' => '290'),
        'SI' => array('name' => 'SLOVENIA', 'code' => '386'),
        'SK' => array('name' => 'SLOVAKIA', 'code' => '421'),
        'SL' => array('name' => 'SIERRA LEONE', 'code' => '232'),
        'SM' => array('name' => 'SAN MARINO', 'code' => '378'),
        'SN' => array('name' => 'SENEGAL', 'code' => '221'),
        'SO' => array('name' => 'SOMALIA', 'code' => '252'),
        'SR' => array('name' => 'SURINAME', 'code' => '597'),
        'ST' => array('name' => 'SAO TOME AND PRINCIPE', 'code' => '239'),
        'SV' => array('name' => 'EL SALVADOR', 'code' => '503'),
        'SY' => array('name' => 'SYRIAN ARAB REPUBLIC', 'code' => '963'),
        'SZ' => array('name' => 'SWAZILAND', 'code' => '268'),
        'TC' => array('name' => 'TURKS AND CAICOS ISLANDS', 'code' => '1649'),
        'TD' => array('name' => 'CHAD', 'code' => '235'),
        'TG' => array('name' => 'TOGO', 'code' => '228'),
        'TH' => array('name' => 'THAILAND', 'code' => '66'),
        'TJ' => array('name' => 'TAJIKISTAN', 'code' => '992'),
        'TK' => array('name' => 'TOKELAU', 'code' => '690'),
        'TL' => array('name' => 'TIMOR-LESTE', 'code' => '670'),
        'TM' => array('name' => 'TURKMENISTAN', 'code' => '993'),
        'TN' => array('name' => 'TUNISIA', 'code' => '216'),
        'TO' => array('name' => 'TONGA', 'code' => '676'),
        'TR' => array('name' => 'TURKEY', 'code' => '90'),
        'TT' => array('name' => 'TRINIDAD AND TOBAGO', 'code' => '1868'),
        'TV' => array('name' => 'TUVALU', 'code' => '688'),
        'TW' => array('name' => 'TAIWAN, PROVINCE OF CHINA', 'code' => '886'),
        'TZ' => array('name' => 'TANZANIA, UNITED REPUBLIC OF', 'code' => '255'),
        'UA' => array('name' => 'UKRAINE', 'code' => '380'),
        'UG' => array('name' => 'UGANDA', 'code' => '256'),
        'US' => array('name' => 'UNITED STATES', 'code' => '1'),
        'UY' => array('name' => 'URUGUAY', 'code' => '598'),
        'UZ' => array('name' => 'UZBEKISTAN', 'code' => '998'),
        'VA' => array('name' => 'HOLY SEE (VATICAN CITY STATE)', 'code' => '39'),
        'VC' => array('name' => 'SAINT VINCENT AND THE GRENADINES', 'code' => '1784'),
        'VE' => array('name' => 'VENEZUELA', 'code' => '58'),
        'VG' => array('name' => 'VIRGIN ISLANDS, BRITISH', 'code' => '1284'),
        'VI' => array('name' => 'VIRGIN ISLANDS, U.S.', 'code' => '1340'),
        'VN' => array('name' => 'VIET NAM', 'code' => '84'),
        'VU' => array('name' => 'VANUATU', 'code' => '678'),
        'WF' => array('name' => 'WALLIS AND FUTUNA', 'code' => '681'),
        'WS' => array('name' => 'SAMOA', 'code' => '685'),
        'XK' => array('name' => 'KOSOVO', 'code' => '381'),
        'YE' => array('name' => 'YEMEN', 'code' => '967'),
        'YT' => array('name' => 'MAYOTTE', 'code' => '262'),
        'ZA' => array('name' => 'SOUTH AFRICA', 'code' => '27'),
        'ZM' => array('name' => 'ZAMBIA', 'code' => '260'),
        'ZW' => array('name' => 'ZIMBABWE', 'code' => '263')
    );
}

function getCountrySortOrder(){
    /*
	Österreich				AT
	Deutschland				DE
	Kroatien				HR
	Polen					PL
	Rumänien				RO
	Russland				RU
	Serbien					XS
	Slowakei				SK
	Slowenien				SI
	Schweiz					CH
	Tschechische Republik	CZ
	Ukraine					UA
	Ungarn					HU
    */
    return [
        "AT",
        "DE",
        "HR",
        "PL",
        "RO",
        "RU",
        "XS",
        "SK",
        "SI",
        "CH",
        "CZ",
        "UA",
        "HU",
    ];
}

function getCsvCurrentUsersData(){
    $array  = $fields = array();
    $i      = 0;
    $handle = @fopen(resource_path("definitions/2021-01-13-accountToMW.csv"), "r");
    if($handle){
        while(($row = fgetcsv($handle, 4096)) !== false){
            $row = array_map("fixCsvEncoding", $row);
            if(empty($fields)){
                $fields = $row;
                continue;
            }
            foreach($row as $k => $value){
                $array[$i][$fields[$k]] = $value;
            }
            $i++;
        }
        if(!feof($handle)){
            echo "Error: unexpected fgets() fail\n";
        }
        fclose($handle);
    }
    return $array;
}

function fixCsvEncoding($str){
    return iconv("Windows-1252", "UTF-8", $str);
}

function getCsvTextTranslation(){
    $array  = $fields = array();
    $i      = 0;
    $handle = @fopen(resource_path("definitions/api_translation.csv"), "r");
    if($handle){
        while(($row = fgetcsv($handle, 4096, ";")) !== false){
            $row = array_map("fixCsvEncoding", $row);
            if(empty($fields)){
                $fields = $row;
                continue;
            }
            foreach($row as $k => $value){
                $array[$i][$fields[$k]] = $value;
            }
            $i++;
        }
        if(!feof($handle)){
            echo "Error: unexpected fgets() fail\n";
        }
        fclose($handle);
    }
    return $array;
}

function getCsvOldUsersData(){
    $array  = $fields = array();
    $i      = 0;
    $handle = @fopen(resource_path("definitions/20201215_AppRegistrierungseit092017.csv"), "r");
    if($handle){
        while(($row = fgetcsv($handle, 4096, ";")) !== false){
            $row = array_map("fixCsvEncoding", $row);
            if(empty($fields)){
                $fields = $row;
                continue;
            }
            foreach($row as $k => $value){
                $array[$i][$fields[$k]] = $value;
            }
            $i++;
        }
        if(!feof($handle)){
            echo "Error: unexpected fgets() fail\n";
        }
        fclose($handle);
    }
    return $array;
}

function getCsvAppTexts($type = "mobile", $lang = "de"){
    $textData = [];
    $i        = 0;
    if(($handle = fopen(resource_path("definitions/" . $type . "_app_" . $lang . "_texts.csv"), "r")) !== FALSE){
        while(($data = fgetcsv($handle, 0, ";")) !== FALSE){
            $cols = count($data);
            if($cols < 2) continue;
            if(++$i < 2) continue; // skip header row
            if(@trim($data[0]) === "key") continue;
            $textData[$data[0]] = $data[1];
        }
        fclose($handle);
    }
    return $textData;
}

function importAppTexts($type = "mobile", $lang = "de"){
    $textData   = getCsvAppTexts($type, $lang);
    $languageId = app(\App\Code\Api\RequestLanguage::class)->getLanguageIdFromCode($lang);
    $languageId = $languageId ?: 3;
    $textGroup  = $type === "pos" ? Text::TEXT_GROUP_POS_APP : Text::TEXT_GROUP_MOBILE_APP;
    foreach($textData as $key => $value){
        Text::firstOrCreate([
            "text_key"    => $key,
            "language_id" => $languageId,
            "text_group"  => $textGroup,
        ], [
            "text_key"    => $key,
            "language_id" => $languageId,
            "text_group"  => $textGroup,
            "text_value"  => $value,
            "text_type"   => Text::TEXT_TYPE_DEFAULT,
        ]);
    }
}

function decodeApiResult($result = ""): array{
    $data = [];
    if(is_json($result)) $data = json_decode($result, true);
    if(isDevEnv()) app(Ajax::class)->action("log", ["value" => $data]);
    //Log::channel("debug")->debug(sprintf("%s::decodeApiResult -> %s", debug_backtrace()[1]['function'], var_export($result, true)));
    return $data;
}

function loginFrontendUserId($userId = null){
    session()->put("frontend_user_id", $userId);
}

function isFrontendUser(){
    return session()->has("frontend_user_id") && session()->get("frontend_user_id");
}

function logoutFrontendUser(){
    if(session()->has("frontend_user_id"))
        session()->forget("frontend_user_id");
}

function getFrontendUser(){
    if(!session()->has("frontend_user_id")) return false;
    $userId = session()->get("frontend_user_id");
    $user   = User::where("id", $userId)->first();
    if(!$user) return false;
    return $user;
}

function getPosAdminInfoList($remove = true){
    $adminInfoList = session()->has("pos_admin_info_list") ? session()->get("pos_admin_info_list") : [];
    if($remove) session()->forget("pos_admin_info_list");
    return $adminInfoList;
}

function addPosAdminInfo($type = "info", $message = ""){
    $infoList   = getPosAdminInfoList(false);
    $infoList[] = ["type" => $type, "message" => $message];
    session()->flash("pos_admin_info_list", $infoList);
}

function loginPosAdminUserId($adminUserId = null){
    session()->put("post_admin_user_id", $adminUserId);
}

function isPosAdminUser(){
    return session()->has("post_admin_user_id") && session()->get("post_admin_user_id");
}

function logoutPosAdminUser(){
    if(session()->has("post_admin_user_id")) session()->forget("post_admin_user_id");
}

function getPosAdminUser(){
    if(!session()->has("post_admin_user_id")) return false;
    $adminUserId = session()->get("post_admin_user_id");
    $adminUser   = Admins::where("id", $adminUserId)->first();
    if(!$adminUser) return false;
    return $adminUser;
}

function getFrontendUserNextStep(){
    $nextStep = "";
    $user     = getFrontendUser();
    if(!$user) return $nextStep;

    // Find-out next step
    /* @var Address $address */
    $address = $user->addresses()->first();
    if(!$user->email && $address && $address->post_code) $nextStep = "login_via_postcode";
    else if(!$user->email && $user->password) $nextStep = "add_email";
    else if($user->email && !$user->password) $nextStep = "add_password";
    else if(!$user->email_verified_at && $user->email && $user->password){
        $nextStep = "confirm_email";
    }
    return $nextStep;
}

function getQrImageData($memberId = ""){
    if(!@trim($memberId)) return "";
    return (new QRCode(new QROptions([
        'scale' => 10,
    ])))->render($memberId);
}

function noNextStatus($status = "", $currentStatus = ""){
    return !@trim($status) || stripos($currentStatus, "Diamon") !== false;
}

function toArray($data){
    if(is_array($data)) return $data;
    return [$data];
}

function finalizeBenefitsData($benefitsData = [], $meData = []){
    $discount        = stripos(@$meData["status"], "plus") !== false ? @$meData["status_discount"] : "10";
    $discount        = $discount ?: "10";
    $outBenefitsData = [];
    foreach($benefitsData as $benefitEntry){
        $benefitEntry["title"]   = str_replace("{{discount}}", $discount, $benefitEntry["title"]);
        $benefitEntry["content"] = str_replace("{{discount}}", $discount, $benefitEntry["content"]);
        $outBenefitsData[]       = $benefitEntry;
    }
    return $outBenefitsData;
}

function markdownToHtml($markdownText = ""){
    $Parsedown    = new Parsedown();
    $markdownText = $Parsedown->text($markdownText);

    /*
    $markdownText = str_replace("\r\n\r\n", "<br><br>", $markdownText);
    $markdownText = str_replace("\r\n*", "<br>*", $markdownText);
    $parser       = new \cebe\markdown\Markdown();
    $markdownText = $parser->parse($markdownText);
    */
    $markdownText = str_replace("\\", "", $markdownText);
    return $markdownText;
}

function detectEmail($str){
    $mail_pattern = "/([A-z0-9\._-]+\@[A-z0-9_-]+\.)([A-z0-9\_\-\.]{1,}[A-z])/";
    $str          = preg_replace($mail_pattern, '<a href="mailto:$1$2" class="detail_inline_mail">$1$2</a>', $str);
    return $str;
}

function autolink($message){
//Convert all urls to links
    $message     = preg_replace('#([\s|^])(www)#i', '$1http://$2', $message);
    $pattern     = '#((http|https|ftp|telnet|news|gopher|file|wais):\/\/[^\s]+)#i';
    $replacement = '<a href="$1" target="_blank">$1</a>';
    $message     = preg_replace($pattern, $replacement, $message);

    /* Convert all E-mail matches to appropriate HTML links */
    $pattern     = '#([0-9a-z]([-_.]?[0-9a-z])*@[0-9a-z]([-.]?[0-9a-z])*\\.';
    $pattern     .= '[a-wyz][a-z](fo|g|l|m|mes|o|op|pa|ro|seum|t|u|v|z)?)#i';
    $replacement = '<a href="mailto:\\1">\\1</a>';
    $message     = preg_replace($pattern, $replacement, $message);
    return $message;
}

function makeLinksClickable($text){
    return preg_replace('!(((f|ht)tp(s)?://)[-a-zA-Zа-яА-Я()0-9@:%_+.~#?&;//=]+)!i', '<a href="$1">$1</a>', $text);
}

function isInteger($input){
    return (ctype_digit((string)$input));
}

function loginAccessToken($token = ""){
    try{
        if(!$token || !userTokenValid($token)){
            Log::error(sprintf("%s - %s (%s)", __FUNCTION__, "Token invalid!", $token));
            return false;
        }
        $data = app(Lcobucci::class)->decode($token);
        $id   = $data["sub"] ?? false;
        if(!$id){
            Log::error(sprintf("%s - %s", __FUNCTION__, "User id not found!"));
            return false;
        }
        $user = User::where("id", $id)->first();
        if(!$user){
            Log::error(sprintf("%s - %s (%s)", __FUNCTION__, "User not found!", $id));
            return false;
        }
        logoutFrontendUser();
        loginFrontendUserId($user->id);
        return true;
    }
    catch(Exception $e){
        Log::error(sprintf("%s - %s", __FUNCTION__, $e->getMessage()));
        return false;
    }
}

function userTokenValid($token = ""): bool{
    try{
        $data = app(Lcobucci::class)->decode($token);
        return ($data["exp"] ?? 0) > time();
    }
    catch(Error $e){
        Log::error(sprintf("%s - %s - %s - %s", __FUNCTION__, $e->getMessage(), $e->getTraceAsString(), $token));
        return false;
    }
}

function importTranslationApiTexts(){
    $textData         = getCsvTextTranslation();
    $germanLanguageId = app(\App\Code\Api\RequestLanguage::class)->getLanguageIdFromCode("de");
    $germanLanguageId = $germanLanguageId ?: 3;
    foreach($textData as $entry){ // German texts
        Text::updateOrCreate([
            "text_key"    => $entry["text_key"],
            "language_id" => $germanLanguageId,
            "text_group"  => Text::TEXT_GROUP_API,
        ], [
            "text_key"    => $entry["text_key"],
            "language_id" => $germanLanguageId,
            "text_group"  => Text::TEXT_GROUP_API,
            "text_value"  => $entry["text_value"],
            "text_type"   => Text::TEXT_TYPE_DEFAULT,
        ]);
    }
}

function getRequestLanguageCode($lower = true){
    $languageCode = app(\App\Code\Api\RequestLanguage::class)->getActiveLanguageCode() ?: "de";;
    return $lower ? strtolower(trim($languageCode)) : trim($languageCode);
}

/**
 * @return \App\User|\Illuminate\Contracts\Auth\Authenticatable|null
 */
function getAuthUser(){
    $user = auth()->user();
    if(!$user) return getFrontendUser();
    else return $user;
}

function redirectToInfo($header = "", $content = ""){
    session()->flash("info_header", $header);
    session()->flash("info_content", $content);
    return redirect()->route("site_info");
}

function getRedirectUrlToInfo($header = "", $content = ""){
    session()->flash("info_header", $header);
    session()->flash("info_content", $content);
    return route("site_info");
}

function getSfStreet($street = "", $streetNumber = "", $streetAdditional = ""){
    $street           = @trim($street);
    $streetNumber     = @trim($streetNumber);
    $streetAdditional = @trim($streetAdditional);

    return $street . " " . $streetNumber . ($streetAdditional ? ("/" . $streetAdditional) : "");
}

function updateUserAppDownloaded($user, $request, $ignoreTimeCheck = false){
    if(!$user || !$request) return;

    // Update previous non-mobile user flag
    if(!$user->app_is_downloaded && !$request->input("via_web_login") && !$request->input("via_web")
        && !session()->has("frontend_user_id") && !session()->has("isLogined")){

        if(!$ignoreTimeCheck && @strtotime($user->created_at) > @strtotime("2021-05-14 10:00:00")) return;

        tryElse(function () use ($user, $request){
            Log::info("Update previous non-mobile user flag = " .
                @ var_export([$request->fullUrl(), $user->app_is_downloaded,
                              $user->source_of_registration, $user->member_id], true));

            /* @var Address $address */
            $address = $user->addresses()->first();

            /* @var Country $country */
            $country = Country::where("id", $user->country_id)->first();

            if($address && $country){
                $salesforceData = [
                    "salesforceId"             => $user->salesforce_id,
                    "email"                    => $user->email,
                    "title"                    => $user->title,
                    "titleAfter"               => $user->title_after,
                    "street"                   => getSfStreet($address->street, $address->street_number, $address->street_additional),
                    "postalCode"               => $address->post_code,
                    "mobile"                   => $user->mobile,
                    "lastName"                 => $user->last_name,
                    "gender"                   => @["m" => "MALE", "f" => "FEMALE",
                                                    "d" => "UNKNOWN"][$user->gender],
                    "firstName"                => $user->first_name,
                    "country"                  => $country->getLanguageCode() ?? "",
                    "city"                     => $address->city,
                    "personHasOptedOutOfEmail" => false,
                    "marketingNewsOptOut"      => $user->newsletter_sub ? false : true,
                    "nlClubNewsOptOut"         => $user->club_newsletter_sub ? false : true,
                    "salutation"               => $user->salutation ?: "",
                    "isEmployee"               => $user->employee_info_id ? true : false,
                    "isAppDownloaded"          => true,
                    "pushAllowed"              => $user->push_allowed ? true : false,
                ];

                if($user->day_of_birth && (correctDate($user->day_of_birth) ||
                        correctDate($user->day_of_birth, "j.n.Y")))
                    $salesforceData["birthdate"] = date("Y-m-d", strtotime($user->day_of_birth));

                $responseData = app(SalesforceMarketingApi::class)
                    ->createOrUpdateAccount($salesforceData);

                $user->app_is_downloaded = true;
                $user->save();

                Log::info(sprintf("%s - User app_is_downloaded updated! %s",
                    __METHOD__, ($user->id ?? 0)));
            }
            else{
                Log::error(sprintf("%s - No address or country -> %s, %s",
                    __METHOD__, var_export([(bool)$user, (bool)$country, (bool)$address], true),
                    ($user->id ?? 0)));
            }
        });

    }
}

function createDefaultTexts(){
    apiText("api:add_email.email.is_empty", "The supplied email is empty.");
    apiText("api:add_email.email.too_long", "The email should not be longer than 128 characters.");
    apiText("api:add_email.email.invalid_email_format", "The email format is not correct.");
    apiText("api:add_email.email.email_already_used", "The email has already been used.");

    apiText("api:change_password.password.is_empty", "The supplied password is empty.");
    apiText("api:change_password.password.too_long", "The password should not be longer than 128 characters.");
    apiText("api:change_password.password.too_short", "The password length should be at least 8 characters.");
    apiText("api:change_password.password.number_missing", "The password should contain at least 1 number.");
    apiText("api:change_password.password.letter_missing", "The password should contain at least 1 letter.");
    apiText("api:change_password.password.uppercase_missing", "The password should contain at least 1 uppercase letter.");
    apiText("api:change_password.password.password_not_different", "The new password is the same as the old one.");

    apiText("api:confirm_email.confirm_code.is_empty", "The supplied value is empty.");
    apiText("api:confirm_email.confirm_code.not_found", "The the confirm code couldn't be found.");
    apiText("api:confirm_email.confirm_code.code_expired", "The the confirm code has expired.");

    apiText("api:login.email.not_found", "The user couldn't be found.");
    apiText("api:login.password.wrong_password", "The entered password is not correct.");

    apiText("api:login_via_member_post_code.member_id.not_found", "The user couldn't be found.");
    apiText("api:login_via_member_post_code.member_id.already_has_email", "The user already has a verified email address attached to it. Use password reset instead.");

    apiText("api:reset_password.email.is_empty", "The supplied email is empty.");
    apiText("api:reset_password.email.too_long", "The email should not be longer than 128 characters.");
    apiText("api:reset_password.email.invalid_email_format", "The email format is not correct.");
    apiText("api:reset_password.email.email_not_found", "The email has not been found.");

    apiText("api:reset_password_confirm.email.is_empty", "The supplied email is empty.");
    apiText("api:reset_password_confirm.password.is_empty", "The supplied password is empty.");
    apiText("api:reset_password_confirm.password.too_long", "The password should not be longer than 128 characters.");
    apiText("api:reset_password_confirm.password.too_short", "The password should be longer than 8 characters.");
    apiText("api:reset_password_confirm.password.number_missing", "The password should contain at least 1 number.");
    apiText("api:reset_password_confirm.password.letter_missing", "The password should contain at least 1 letter.");
    apiText("api:reset_password_confirm.password.uppercase_missing", "The password should contain at least 1 uppercase letter.");
    apiText("api:reset_password_confirm.email.not_found", "The the reset code couldn't be found.");
    apiText("api:reset_password_confirm.email.code_expired", "The the reset code has expired.");

    apiText("api:validate_change_data.first_name.too_long", "The first name should not be longer than 128 characters.");
    apiText("api:validate_change_data.last_name.too_long", "The last name should not be longer than 128 characters.");
    apiText("api:validate_change_data.gender.invalid_value", "Only the following gender values are allowed: \"m|f|d\".");
    apiText("api:validate_change_data.title.too_long", "The title should not be longer than 128 characters.");
    apiText("api:validate_change_data.country_id.invalid_id", "The country id is invalid.");
    apiText("api:validate_change_data.day_of_birth.invalid_format", "Day of birth must be the following format: Y-m-d Example: 2020-09-20");
    apiText("api:validate_change_data.day_of_birth.invalid_value", "The entered day of birth is not valid.");
    apiText("api:validate_change_data.day_of_birth.too_young", "The user needs to be at least 18 years old.");
    apiText("api:validate_change_data.mobile.too_long", "The mobile number should not be longer than 128 characters.");
    apiText("api:validate_change_data.newsletter_sub.invalid_value", "Only the following newsletter usage values are allowed: \"y|n\".");
    apiText("api:validate_change_data.email.too_long", "The email should not be longer than 128 characters.");
    apiText("api:validate_change_data.email.invalid_email_format", "The email format is not correct.");
    apiText("api:validate_change_data.email.email_domain_blocked", "The email domain is blocked.");
    apiText("api:validate_change_data.email.email_already_used", "The email has already been used.");
    apiText("api:validate_change_data.street.too_long", "The street should not be longer than 128 characters.");
    apiText("api:validate_change_data.street_number.too_long", "The street number should not be longer than 128 characters.");
    apiText("api:validate_change_data.street_additional.too_long", "The street additional should not be longer than 128 characters.");
    apiText("api:validate_change_data.post_code.too_long", "The postcode should not be longer than 128 characters.");
    apiText("api:validate_change_data.city.too_long", "The city should not be longer than 128 characters.");

    apiText("api:validate_feedback.first_name.empty", "The first name is empty.");
    apiText("api:validate_feedback.first_name.too_long", "The first name should not be longer than 128 characters.");
    apiText("api:validate_feedback.last_name.empty", "The last name is empty.");
    apiText("api:validate_feedback.last_name.too_long", "The last name should not be longer than 128 characters.");
    apiText("api:validate_feedback.email.empty", "The email is empty.");
    apiText("api:validate_feedback.email.too_long", "The email should not be longer than 128 characters.");
    apiText("api:validate_feedback.email.invalid_email_format", "The email format is not correct.");
    apiText("api:validate_feedback.email.email_domain_blocked", "The email domain is blocked.");
    apiText("api:validate_feedback.content.empty", "The content is empty.");
    apiText("api:validate_feedback.content.too_long", "The content should not be longer than 5000 characters.");

    apiText("api:validate_login.email.is_empty", "The supplied email is empty.");
    apiText("api:validate_login.email.too_long", "The email should not be longer than 128 characters.");
    apiText("api:validate_login.email.invalid_email_format", "The email format is not correct.");
    apiText("api:validate_login.password.is_empty", "The supplied password is empty.");

    apiText("api:validate_login_old.member_id.is_empty", "The supplied member id is empty.");
    apiText("api:validate_login_old.member_id.too_long", "The member id should not be longer than 15 characters.");
    apiText("api:validate_login_old.post_code.is_empty", "The supplied postcode is empty.");
    apiText("api:validate_login_old.post_code.too_long", "The postcode should not be longer than 20 characters.");

    apiText("api:validate_register.first_name.is_empty", "The supplied first name is empty.");
    apiText("api:validate_register.first_name.too_long", "The first name should not be longer than 128 characters.");
    apiText("api:validate_register.last_name.is_empty", "The supplied last name is empty.");
    apiText("api:validate_register.last_name.too_long", "The last name should not be longer than 128 characters.");
    apiText("api:validate_register.gender.is_empty", "The supplied gender is empty.");
    apiText("api:validate_register.gender.invalid_value", "Only the following gender values are allowed: \"m|f|d\".");
    apiText("api:validate_register.title.is_empty", "The title should not be longer than 128 characters.");
    apiText("api:validate_register.country_id.invalid_id", "The country id is invalid.");
    apiText("api:validate_register.country_id.is_empty", "The supplied country id is empty.");
    apiText("api:validate_register.day_of_birth.is_empty", "The supplied day of birth is empty.");
    apiText("api:validate_register.day_of_birth.invalid_format", "Day of birth must be the following format: Y-m-d Example: 2020-09-20");
    apiText("api:validate_register.day_of_birth.invalid_value", "The entered day of birth is not valid.");
    apiText("api:validate_register.day_of_birth.too_young", "The user needs to be at least 18 years old.");
    apiText("api:validate_register.mobile.too_long", "The mobile number should not be longer than 128 characters.");
    apiText("api:validate_register.newsletter_sub.is_empty", "The supplied newsletter confirmation is empty.");
    apiText("api:validate_register.newsletter_sub.invalid_value", "Only the following newsletter usage values are allowed: \"y|n\".");
    apiText("api:validate_register.email.is_empty", "The supplied email is empty.");
    apiText("api:validate_register.email.too_long", "The email should not be longer than 128 characters.");
    apiText("api:validate_register.email.invalid_email_format", "The email format is not correct.");
    apiText("api:validate_register.email.email_domain_blocked", "The email domain is blocked.");
    apiText("api:validate_register.email.email_already_used", "The email has already been used.");
    apiText("api:validate_register.password.is_empty", "The supplied password is empty.");
    apiText("api:validate_register.password.too_long", "The password should not be longer than 128 characters.");
    apiText("api:validate_register.password.too_short", "The password should be longer than 8 characters.");
    apiText("api:validate_register.password.number_missing", "The password should contain at least 1 number.");
    apiText("api:validate_register.password.letter_missing", "The password should contain at least 1 letter.");
    apiText("api:validate_register.password.uppercase_missing", "The password should contain at least 1 uppercase letter.");
    apiText("api:validate_register.street.is_empty", "The supplied street is empty.");
    apiText("api:validate_register.street.too_long", "The street should not be longer than 128 characters.");
    apiText("api:validate_register.street_number.is_empty", "The supplied street_number is empty.");
    apiText("api:validate_register.street_number.too_long", "The street_number should not be longer than 128 characters.");
    apiText("api:validate_register.street_additional.too_long", "The street_additional should not be longer than 128 characters.");
    apiText("api:validate_register.post_code.is_empty", "The supplied post_code is empty.");
    apiText("api:validate_register.post_code.too_long", "The post_code should not be longer than 128 characters.");
    apiText("api:validate_register.city.is_empty", "The supplied city is empty.");
    apiText("api:validate_register.city.too_long", "The city should not be longer than 128 characters.");

    apiText("api:check_email.email.is_empty", "The supplied email is empty.");
    apiText("api:check_email.email.too_long", "The email should not be longer than 128 characters.");
    apiText("api:check_email.email.invalid_email_format", "The email format is not correct.");
    apiText("api:check_email.email.email_domain_blocked", "The email domain is blocked.");
    apiText("api:check_email.email.email_already_used", "The email has already been used.");

    apiText("api:check_member_id.member_id.is_empty", "The member id is empty.");
    apiText("api:check_member_id.member_id.member_id_not_found", "The member id has not been used.");

    apiText("api:confirm_email_newsletter.confirm_code.is_empty", "The supplied value is empty.");
    apiText("api:confirm_email_newsletter.confirm_code.not_found", "The the confirm code couldn't be found.");
    apiText("api:confirm_email_newsletter.confirm_code.code_expired", "The the confirm code has expired.");

    apiText("api:validate_register.title_after.is_empty", "The title after should not be longer than 128 characters.");
    apiText("api:validate_change_data.title_after.too_long", "The title after should not be longer than 128 characters.");

    emailText("reset_old_pw_mail:mail.salutation.m", "Sehr geehrter Herr [last_name]");
    emailText("reset_old_pw_mail:mail.salutation.f", "Sehr geehrte Frau [last_name]");
    emailText("reset_old_pw_mail:mail.salutation.d", "Sehr geehrte*r [first_name]-[last_name]! (inter)");

    apiText("api:validate_register.password_repeat.is_empty", "The supplied repeated password is empty.");
    apiText("api:validate_register.password_repeat.not_match", "The entered passwords no not match.");
    apiText("api:validate_register.terms_accepted.not_accepted", "You need to accept the terms to continue.");

    apiText("api:change_password.password_confirm.no_match", "The entered passwords must match.");
    apiText("api:change_password.password_confirm.empty", "Please repeat your new password");

    apiText("api:validate_change_data.email_confirm.empty", "Please enter your new email address.");
    apiText("api:validate_change_data.email_confirm.no_match", "The entered email addresses don't match.");

    apiText("api:reset_password_confirm.reset_code.is_empty", "The supplied reset token is missing.");
    apiText("api:check_member_id.member_id.member_id_has_password", "The member id is already registered.");

    posAppText("debug:test.pos.text", "Some text");

    apiText("api:validate_register.first_name.invalid_format", "Invalid format.");
    apiText("api:validate_register.last_name.invalid_format", "Invalid format.");
    apiText("api:validate_register.mobile.invalid_format", "Invalid format.");
    apiText("api:validate_change_data.first_name.invalid_format", "Invalid format.");
    apiText("api:validate_change_data.last_name.invalid_format", "Invalid format.");
    apiText("api:validate_change_data.mobile.invalid_format", "Invalid format.");

    apiText("api:validate_register.first_name.too_short", "The first name should be at least 2 characters.");
    apiText("api:validate_register.last_name.too_short", "The last name should be at least 2 characters.");
    apiText("api:validate_change_data.first_name.too_short", "The first name should be at least 2 characters.");
    apiText("api:validate_change_data.last_name.too_short", "The last name should be at least 2 characters.");

    apiText("api:validate_register.street.invalid_format", "Invalid format.");
    apiText("api:validate_change_data.street.invalid_format", "Invalid format.");
    apiText("api:validate_login_old.post_code.invalid_format", "Invalid format.");
    apiText("api:validate_login_old.post_code.too_short", "The postcode should be at least 4 characters.");
    apiText("api:validate_register.city.invalid_format", "Invalid format.");
    apiText("api:validate_change_data.city.invalid_format", "Invalid format.");
    apiText("api:validate_login_old.post_code.too_short", "The postcode should be at least 4 characters.");
    apiText("api:validate_login_old.post_code.invalid_format", "Invalid format.");
    apiText("api:validate_register.post_code.too_short", "The postcode should be at least 4 characters.");
    apiText("api:validate_register.post_code.invalid_format", "Invalid format.");
    apiText("api:validate_change_data.post_code.too_short", "The postcode should be at least 4 characters.");
    apiText("api:validate_change_data.post_code.invalid_format", "Invalid format.");

    apiText("api:validate_register.street_number.invalid_format", "Invalid format.");
    apiText("api:validate_change_data.street_number.invalid_format", "Invalid format.");

    apiText("api:validate_register.street_additional.invalid_format", "Invalid format.");
    apiText("api:validate_change_data.street_additional.invalid_format", "Invalid format.");

    webText("info:confirm_mail.invalid.header", "E-Mail Bestätigung fehlgeschlagen!");
    webText("info:confirm_mail.expired.header", "E-Mail Bestätigung fehlgeschlagen!");
    webText("info:confirm_mail.expired.content", "Der Confirm-Token ist abgelaufen.");

    webText("info:confirm_newsletter.invalid.header", "E-Mail Bestätigung fehlgeschlagen!");
    webText("info:confirm_newsletter.success.header", "Newsletter Anmeldung erfolgreich!");
    webText("info:confirm_newsletter.success.content", "Ihre Anmeldung zum Newsletter war erfolgreich.");
    webText("info:confirm_newsletter.error.header", "Newsletter Anmeldung fehlgeschlagen!");
    webText("info:confirm_newsletter.error.content", "Ihre Newsletter Anmeldung ist leider fehlgeschlagen.");

    webText("info:reset_password.invalid.header", "Passwort zurücksetzen fehlgeschlagen!");
    webText("info:reset_password.expired.header", "Passwort zurücksetzen fehlgeschlagen!");
    webText("info:reset_password.expired.content", "Der Reset-Token ist abgelaufen.");
    webText("info:reset_password.expired.header", "Passwort zurücksetzen fehlgeschlagen!");
    webText("info:reset_password.expired.content", "Der Reset-Token ist abgelaufen.");
    webText("info:reset_password.expired.header", "Passwort zurücksetzen fehlgeschlagen!");
    webText("info:reset_password.expired.content", "Der Reset-Token ist abgelaufen.");

    webText("info:reset_password_old_account.invalid.header", "Passwort zurücksetzen fehlgeschlagen!");
    webText("info:reset_password_old_account.expired.header", "Passwort zurücksetzen fehlgeschlagen!");
    webText("info:reset_password_old_account.expired.content", "Der Reset-Token ist abgelaufen.");
    webText("info:reset_password_old_account.expired.header", "Passwort zurücksetzen fehlgeschlagen!");
    webText("info:reset_password_old_account.expired.content", "Der Reset-Token ist abgelaufen.");
}

?>
