<?php

namespace App\Console\Commands;

use App\Code\External\Api\SalesforceMarketingApi;
use App\Code\External\Api\StylaContentApi;
use App\Http\Models\Api\User;
use Illuminate\Console\Command;

class Debug extends Command{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'debug';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Debug';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(){
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle(){

        User::createUserFromSalesforce("64FMDZ9KM", "<EMAIL>");
        die;
        $salesforceMarketing = app(SalesforceMarketingApi::class);
        User::apiFetchUserTitles($salesforceMarketing);
        User::apiFetchCountries($salesforceMarketing);

        die;

        $responseData  = [
            "email"      => "<EMAIL>",
            "cardNumber" => "43E948EA",
        ];
        $whereEmail    = "<EMAIL>";
        $whereMemberId = "43E948EA";
        if($whereMemberId){
            $user = \App\Http\Models\Api\User::getByField("member_id", $whereMemberId);
            $this->info("Found user by memberid: "  .($user ? "Y" : "N"));
            $user = $user ?: (($responseData["email"] ?? false) ?
                \App\Http\Models\Api\User::getByField("email", $responseData["email"]) : $user);
            $this->info("Found user by optional email over memberid: "  .($user ? "Y" : "N"));
        }
        elseif($whereEmail){
            $user = \App\Http\Models\Api\User::getByField("email", $whereEmail);
            $this->info("Found user by email: "  .($user ? "Y" : "N"));
            $user = $user ?: (($responseData["cardNumber"] ?? false) ?
                \App\Http\Models\Api\User::getByField("member_id", $responseData["cardNumber"]) : $user);
            $this->info("Found user by optional memberid over email: "  .($user ? "Y" : "N"));
        }


        die;

        ini_set('max_execution_time', '1200');
        ini_set('memory_limit', '2048M');

        //User::apiFetchUserTitles($salesforceMarketing);
        //User::apiFetchCountries($salesforceMarketing);
        User::apiFetchUserVouchers($salesforceMarketing, true);
        //User::apiFetchUserCampaigns($salesforceMarketing, true);
        //User::apiFetchUserPurchases($salesforceMarketing, true);
        //app(StylaContentApi::class)->syncAllFeeds();


        die;

        //app(StylaContentApi::class)->syncAllFeeds();

        $provider    = new \League\OAuth2\Client\Provider\GenericProvider([
            'clientId'                => config("external_api.salesforce_marketing_client_id"),
            'clientSecret'            => config("external_api.salesforce_marketing_client_secret"),
            'redirectUri'             => config("external_api.salesforce_marketing_redirect_uri"),
            'urlAuthorize'            => config("external_api.salesforce_marketing_url_access_token"),
            'urlAccessToken'          => config("external_api.salesforce_marketing_url_access_token"),
            'urlResourceOwnerDetails' => config("external_api.salesforce_marketing_url_access_token"),
        ]);
        $accessToken = $provider->getAccessToken('password', [
            'username' => config("external_api.salesforce_marketing_username"),
            'password' => config("external_api.salesforce_marketing_password"),
        ]);
        $registerUrl = $accessToken->getValues()["instance_url"] . config("external_api.salesforce_marketing_url_path_registration");
        $request     = $provider->getAuthenticatedRequest(
            "GET",
            $registerUrl . "?" . http_build_query(["cardNumber" => "91612404"]),
            $accessToken
        );
        $data        = $provider->getParsedResponse($request);
        dd($data);

        // 9B8BA412 | 91612404

        /*
        Mail::send(new ConfirmApiEmail("aa", "bb", "cc", "dd", "", "<EMAIL>"));
        Mail::send(new ApiErrorMail(new Exception(), new Request(), "<EMAIL>"));
        Mail::send(new FeedbackMail("aa", "bb", "<EMAIL>", "content", "<EMAIL>"));
        Mail::send(new ForgotPasswordEmail(new Admins(), "<EMAIL>"));
        Mail::send(new ResetApiPasswordEmail("abc", "<EMAIL>", "first name", "last name", "m"));
        Mail::send(new ResetOldApiPasswordEmail("abc", "<EMAIL>", "first name", "last name", "m"));
        Mail::send(new WelcomeNlApiEmail("abc", "awf", "awf", "afw", "<EMAIL>"));
        */
    }
}
