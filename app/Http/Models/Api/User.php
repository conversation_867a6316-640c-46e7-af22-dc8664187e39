<?php

namespace App\Http\Models\Api;

use App\Code\Api\RequestLanguage;
use App\Code\External\Api\SalesforceMarketingApi;
use App\Http\Models\SiteLogs;
use App\Http\Traits\EncryptableEloquentTrait;
use App\Http\Traits\ManageableModelTrait;
use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;
use League\OAuth2\Client\Provider\Exception\IdentityProviderException;
use <PERSON><PERSON>\JWTAuth\Contracts\JWTSubject;

/**
 * @property integer id
 * @property integer language_id
 * @property string  member_id
 * @property string  salesforce_id
 * @property string  person_contact_id
 * @property string  status
 * @property string  status_discount
 * @property string  next_status
 * @property string  next_status_amount
 * @property string  next_status_percent
 * @property string  first_name
 * @property string  last_name
 * @property string  gender
 * @property string  title
 * @property string  title_after
 * @property string  salutation
 * @property integer country_id
 * @property string  day_of_birth
 * @property string  mobile
 * @property boolean terms_accepted
 * @property boolean doi_status
 * @property boolean newsletter_sub
 * @property boolean club_newsletter_sub
 * @property boolean club_status_newsletter_sub
 * @property boolean birthday_newsletter_sub
 * @property boolean is_employee
 * @property boolean employee_info_id
 * @property string  name
 * @property string  email
 * @property string  email_verified_at
 * @property string  email_confirm_code
 * @property string  email_confirm_timeout
 * @property string  email_nl_verified_at
 * @property string  email_nl_confirm_code
 * @property string  email_nl_confirm_timeout
 * @property string  password
 * @property string  reset_token
 * @property string  reset_valid_till
 * @property string  card_expire_at
 * @property string  status_expire_at
 * @property string  registration_date
 * @property bool    app_is_downloaded
 * @property string  source_of_registration
 * @property bool    test_user
 * @property boolean deleted_via_gdpr
 * @property string  last_push
 * @property boolean push_allowed
 * @property string  registration_language
 * @property boolean change_from_pos
 * @property string  created_at
 * @property string  updated_at
 * @property string  deleted_at
 *
 * @mixin Builder
 * @mixin Eloquent
 */
class User extends Authenticatable implements JWTSubject{
    use ManageableModelTrait;
    use EncryptableEloquentTrait;
    use Notifiable;
    use SoftDeletes;

    public const SILVER_THRESHOLD   = 0.00;
    public const GOLD_THRESHOLD     = 1000.00;
    public const PLATINUM_THRESHOLD = 3000.00;
    public const DIAMOND_THRESHOLD  = 5000.00;
    public const EMPLOYEE_THRESHOLD = 0.00;

    protected static $NON_ID_FIELDS = [
        "language_id"                => ["type" => "big_integer", "unsigned" => true, "null" => true],
        "member_id"                  => ["type" => "string", "length" => 64, "null" => true],
        "salesforce_id"              => ["type" => "string", "length" => 64, "null" => true],
        "person_contact_id"          => ["type" => "string", "length" => 64, "null" => true],
        "status"                     => ["type" => "string", "length" => 64, "null" => true],
        "status_discount"            => ["type" => "string", "length" => 64, "null" => true],
        "next_status"                => ["type" => "string", "length" => 64, "null" => true],
        "next_status_amount"         => ["type" => "string", "length" => 64, "null" => true],
        "next_status_percent"        => ["type" => "string", "length" => 64, "null" => true],
        "first_name"                 => ["type" => "text", "null" => true, "change_def" => true],
        "last_name"                  => ["type" => "text", "null" => true, "change_def" => true],
        "gender"                     => ["type" => "string", "length" => 64, "null" => true],
        "title"                      => ["type" => "string", "length" => 128, "null" => true],
        "title_after"                => ["type" => "string", "length" => 128, "null" => true],
        "salutation"                 => ["type" => "string", "length" => 128, "null" => true],
        "country_id"                 => ["type" => "big_integer", "unsigned" => true, "null" => true],
        "day_of_birth"               => ["type" => "text", "null" => true, "change_def" => true],
        "mobile"                     => ["type" => "text", "null" => true, "change_def" => true],
        "terms_accepted"             => ["type" => "bool", "null" => true],
        "doi_status"                 => ["type" => "bool", "null" => true],
        "newsletter_sub"             => ["type" => "bool", "null" => true],
        "club_newsletter_sub"        => ["type" => "bool", "null" => true],
        "club_status_newsletter_sub" => ["type" => "bool", "null" => true],
        "birthday_newsletter_sub"    => ["type" => "bool", "null" => true],
        "is_employee"                => ["type" => "bool", "null" => true],
        "employee_info_id"           => ["type" => "big_integer", "unsigned" => true, "null" => true],
        "name"                       => ["type" => "text", "null" => true, "change_def" => true],
        "email"                      => ["type" => "text", "null" => true, "change_def" => true],
        "email_verified_at"          => ["type" => "datetime", "null" => true],
        "email_confirm_code"         => ["type" => "string", "length" => 100, "null" => true],
        "email_confirm_timeout"      => ["type" => "datetime", "null" => true],
        "email_nl_verified_at"       => ["type" => "datetime", "null" => true],
        "email_nl_confirm_code"      => ["type" => "string", "length" => 100, "null" => true],
        "email_nl_confirm_timeout"   => ["type" => "datetime", "null" => true],
        "password"                   => ["type" => "text", "null" => true],
        "reset_token"                => ["type" => "string", "length" => 100, "null" => true],
        "reset_valid_till"           => ["type" => "datetime", "null" => true],
        "card_expire_at"             => ["type" => "datetime", "null" => true],
        "status_expire_at"           => ["type" => "datetime", "null" => true],
        "registration_date"          => ["type" => "date", "null" => true],
        "test_user"                  => ["type" => "bool", "null" => true],
        "source_of_registration"     => ["type" => "string", "length" => 100, "null" => true],
        "app_is_downloaded"          => ["type" => "bool", "null" => true], // "Mobile App" || "Website" || "Store"
        "deleted_via_gdpr"           => ["type" => "bool", "null" => true],
        "last_push"                  => ["type" => "datetime", "null" => true],
        "push_allowed"               => ["type" => "bool", "null" => true],
        "registration_language"      => ["type" => "string", "length" => 100, "null" => true],
        "change_from_pos"            => ["type" => "bool", "null" => true],
        // ---
        "deleted_at"                 => ["type" => "deleted_at", "null" => true],
        // ---
        "created_at"                 => ["type" => "created_at", "null" => true],
        "updated_at"                 => ["type" => "updated_at"],
    ];

    protected $table       = 'users';
    protected $guarded     = [];
    protected $encryptable = [
        "first_name",
        "last_name",
        "name",
        "email",
        "mobile",
        "day_of_birth",
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token', 'deleted_at', 'email_verified_at', 'terms_accepted',
    ];

    /**
     * @param $value
     *
     * @return float
     */
    /*
    public function getNextStatusPercentAttribute($value): float{
        if(@strtotime($this->created_at) > @strtotime("2021-01-08") && @(int)$value) return $value;
        return static::calcNextStatusPercent($this->status, $this->next_status_amount);
    }
    */

    /**
     * @param User $user
     *
     * @return bool
     */
    public static function deleteViaGdpr(User $user){
        if(!$user) return false;

        // Logging
        SiteLogs::add("User::deleteViaGdpr()", ["user_id" => $user->id]);

        // Anonymize user
        $user->first_name       = "GDPR";
        $user->last_name        = "Invalid." . $user->id;
        $user->name             = $user->first_name . " " . $user->last_name;
        $user->email            = Str::slug($user->first_name) . "-" . Str::slug($user->last_name) . "@gdpr.invalid";
        $user->day_of_birth     = mt_rand(0, 1) == 0 ?
            Carbon::createFromTimestamp(strtotime($user->day_of_birth))->addDays(mt_rand(10, 50)) :
            Carbon::createFromTimestamp(strtotime($user->day_of_birth))->subDays(mt_rand(10, 365));
        $user->mobile           = "0123 456789";
        $user->password         = "";
        $user->deleted_via_gdpr = true;
        $user->title            = null;
        $user->title_after      = null;
        $user->gender           = "d";
        $user->save();

        /* @var Address[] $addresss */
        $addresses = $user->addresses()->get();
        foreach($addresses as $address){
            // Anonymize address
            $address->street            = "GDPR Street";
            $address->street_number     = mt_rand(1, 99);
            $address->street_additional = "";
            $address->post_code         = "0000";
            $address->city              = "GDPR City";
            $address->deleted_via_gdpr  = true;
            $address->save();
        }

        /* @var UserPurchase[] $userPurchases */
        $userPurchases = $user->purchases()->get();
        foreach($userPurchases as $userPurchase){
            // Anonymize purchase
            if($userPurchase->purchase_date)
                $userPurchase->purchase_date = Carbon::createFromTimestamp(strtotime($userPurchase->time))->addHours(mt_rand(1, 999))->format("Y-m-d");
            if($userPurchase->total_amount){
                $userPurchase->total_amount = mt_rand($userPurchase->total_amount - 100, $userPurchase->total_amount + 100);
                if($userPurchase->total_amount < 1) $userPurchase->total_amount = mt_rand(1, 10);
            }
            $userPurchase->purchase_id      = "GDPR Purchase";
            $userPurchase->deleted_via_gdpr = true;
            $userPurchase->save();
        }

        return true;
    }

    /**
     * @param string $firstName
     * @param string $lastName
     *
     * @return User[]|null
     * @throws IdentityProviderException
     */
    public static function createAllUsersFromSalesforceName($firstName = "", $lastName = ""){
        $responseDataSf = app(SalesforceMarketingApi::class)->getAllAccountsViaName($firstName, $lastName);
        $outUsers       = [];
        foreach($responseDataSf as $responseDataSfEntry){
            $memberId = $responseDataSfEntry["accountInfo"]["cardNumber"] ?? false;
            if(!$memberId) continue;
            $user = static::createUserFromSalesforce("", "", $responseDataSfEntry);
            if(!$user) continue;
            $outUsers[] = $user;
        }
        return $outUsers;
    }

    /**
     * @param string $whereMemberId
     * @param string $whereEmail
     * @param bool   $usePreparedApiResult
     *
     * @return User|null
     * @throws IdentityProviderException
     */
    public static function createUserFromSalesforce($whereMemberId = "", $whereEmail = "",
                                                    $usePreparedApiResult = false){
        $user = null;

        // Debug
        $callFunction = "";
        try{
            $caller       = debug_backtrace()[1];
            $callFunction = (isset($caller['class']) ? $caller['class'] . $caller['type'] : '') . $caller['function'];
        }
        catch(\Error $e){

        }
        @Log::info(__METHOD__ . "() - member_id=" . $whereMemberId . ", email=" . $whereEmail
            . ", use_prepared_api_result=" . ($usePreparedApiResult ? "Y" : "N") . ", called_via=" . @$callFunction);

        // Fetch user from salesforce api
        $responseDataSf = [];
        if(!$usePreparedApiResult){
            if($whereMemberId)
                $responseDataSf = app(SalesforceMarketingApi::class)->getAccountViaCardNum($whereMemberId);
            elseif($whereEmail)
                $responseDataSf = app(SalesforceMarketingApi::class)->getAccountViaEmail($whereEmail);
        }
        else{
            $responseDataSf = $usePreparedApiResult;
        }

        // Create or update user
        $responseData       = $responseDataSf["accountInfo"] ?? $responseDataSf;
        $responseCardNumber = $responseData["cardNumber"] ?? false;
        $responseEmail      = $responseData["email"] ?? false;
        $methodUsed         = "-";
        if($responseData){
            /* @var User $user */
            if($whereMemberId){
                $methodUsed = "via_member_id";
                $user       = User::getByField("member_id", $whereMemberId);
                if(!$user && $responseEmail){
                    $methodUsed = "via_member_id_second_via_email";
                    $user       = User::getByField("email", $responseEmail);
                }
            }
            elseif($whereEmail){
                $methodUsed = "via_email";
                $user       = User::getByField("email", $whereEmail);
                if(!$user && $responseCardNumber){
                    $methodUsed = "via_email_second_via_member_id";
                    $user       = User::getByField("member_id", $responseCardNumber);
                }
            }

            Log::info(__METHOD__ . "() - Found user in database via: " . $methodUsed . ", member_id=" .
                $responseCardNumber . ", email=" . $responseEmail . ", found=" . ($user ? "Y" : "N"));

            // Try without getByField
            if(!$user){
                $user = User::where("member_id", $responseCardNumber)->first();
                Log::info(__METHOD__ . "() - Tried via second method. found=" . ($user ? "Y" : "N"));
            }

            // Create or update user
            $user              = $user ?: new User();
            $user->member_id   = $whereMemberId ?: @$responseData["cardNumber"];
            $user->first_name  = @$responseData["firstName"];
            $user->last_name   = @$responseData["lastName"];
            $user->name        = @$responseData["firstName"] . " " . @$responseData["lastName"];
            $user->gender      = stripos(@$responseData["gender"], "FEMALE") !== false ? "f" : (stripos(@$responseData["gender"], "MALE") !== false ? "m" : "d");
            $user->title       = @$responseData["title"] ?: "";
            $user->title_after = @$responseData["titleAfter"] ?: "";
            if(!$user->language_id) $user->language_id = app(RequestLanguage::class)->getActiveLanguageId();

            /* @var Country $country */
            $language = Language::where("code", @$responseData["country"])->first();
            $country  = $language ? Country::where("language_id", $language->id)
                                           ->where("translation_id",
                                               app(RequestLanguage::class)->getActiveLanguageId())->first() :
                Country::where("language_id", $language->id)->first();

            $user->country_id = $country->id ?? 0;

            if(isset($responseData["birthdate"]))
                $user->day_of_birth = date("Y-m-d", @strtotime(@$responseData["birthdate"]));

            if(isset($responseData["registration_date"]))
                $user->registration_date = date("Y-m-d", @strtotime($responseData["registration_date"] ?? ""));

            $user->mobile                     = $responseData["mobile"] ?? @$responseData["phone"];
            $user->terms_accepted             = 1;
            $user->newsletter_sub             = (isset($responseData["marketingNewsOptOut"]) && $responseData["marketingNewsOptOut"]) ? 1 : 0;
            $user->club_newsletter_sub        = (isset($responseData["nlClubNewsOptOut"]) && $responseData["nlClubNewsOptOut"]) ? 1 : 0;
            $user->club_status_newsletter_sub = (isset($responseData["nlStatusInfoOptOut"]) && $responseData["nlStatusInfoOptOut"]) ? 1 : 0;
            $user->birthday_newsletter_sub    = (isset($responseData["nlBirthdayOptOut"]) && $responseData["nlBirthdayOptOut"]) ? 1 : 0;
            $user->email                      = @$responseData["email"];
            if(@trim($user->email)) $user->email_verified_at = Carbon::now()->format("Y-m-d H:i:s");

            $user->salesforce_id = @$responseData["salesforceId"];
            $user->status        = $responseData["accountStatus"] ?? ($responseData["status"] ?? "");

            if(isset($responseData["personContactId"])) $user->person_contact_id = $responseData["personContactId"];
            if(isset($responseData["title"])) $user->title = $responseData["title"];
            if(isset($responseData["titleAfter"])) $user->title_after = $responseData["titleAfter"];
            if(isset($responseData["salutation"])) $user->salutation = $responseData["salutation"];
            if(isset($responseData["nextLevelOfMembership"])) $user->next_status = $responseData["nextLevelOfMembership"];
            if(isset($responseData["isEmployee"])) $user->is_employee = $responseData["isEmployee"];
            if(isset($responseData["pushAllowed"])) $user->push_allowed = $responseData["pushAllowed"];
            if(isset($responseData["amountToNextLevel"])) $user->next_status_amount = $responseData["amountToNextLevel"];
            if(isset($responseData["statusExpirationDate"])) $user->status_expire_at = date("Y-m-d H:i:s", @strtotime($responseData["statusExpirationDate"]));
            if(isset($responseData["statusDiscount"])) $user->status_discount = $responseData["statusDiscount"];
            if(isset($responseData["accountStatusDiscount"])) $user->status_discount = $responseData["accountStatusDiscount"];
            if(isset($responseData["percentToNextLevel"])) $user->next_status_percent = $responseData["percentToNextLevel"];
            if(isset($responseData["registrationLanguage"])) $user->registration_language = $responseData["registrationLanguage"];

            if(isset($responseDataSf["changeFromPOS"])) $user->change_from_pos = $responseDataSf["changeFromPOS"] ? true : false;

            $user->save();

            $street = @$responseData["street"];
            // Street 11/a
            if(stripos($street, "/") !== false && count(explode("/", $street)) < 2){
                $streetCrater          = explode("/", $street);
                $streetAndNumber       = $streetCrater[0] ?? "";
                $streetAdditional      = $streetCrater[1] ?? "";
                $streetAndNumberCrater = explode(" ", $streetAndNumber);
                $streetShort           = $streetAndNumberCrater[0] ?? "";
                $streetNumber          = $streetAndNumberCrater[1] ?? "";
            }
            // Street/11/a
            else if(stripos($street, "/") !== false){
                $streetCrater     = explode("/", $street);
                $streetShort      = $streetCrater[0] ?? "";
                $streetNumber     = $streetCrater[1] ?? "";
                $streetAdditional = $streetCrater[2] ?? "";
            }
            // Street 11 a
            else{
                $streetCrater     = preg_split("/(\d.*)/", $street, -1, PREG_SPLIT_NO_EMPTY | PREG_SPLIT_DELIM_CAPTURE);
                $streetCrater2    = explode(" ", ($streetCrater[1] ?? ""));
                $streetShort      = $streetCrater[0] ?? "";
                $streetNumber     = $streetCrater2[0] ?? "";
                $streetAdditional = implode(" ",
                    array_slice($streetCrater2, 1, count($streetCrater2) - 1));
            }

            // Create or update address
            /* @var Address $address */
            $address                    = Address::where("user_id", $user->id)->first();
            $address                    = $address ? $address : new Address();
            $address->user_id           = $user->id;
            $address->street            = $streetShort;
            $address->street_number     = $streetNumber;
            $address->street_additional = $streetAdditional;
            $address->post_code         = @$responseData["postalCode"];
            $address->city              = @$responseData["city"];
            $address->save();

            // Create or update employee info
            $responseData = $responseDataSf;
            if(isset($responseData["employeeInfo"]) && is_array($responseData["employeeInfo"])){
                /* @var EmployeeInfo $employeeInfo */
                $employeeInfo = $user->employeeInfo()->first();

                $employeeInfo                   = $employeeInfo ? $employeeInfo : new EmployeeInfo();
                $employeeInfo->photo_gfx        = @$responseData["employeeInfo"]["photo"];
                $employeeInfo->company_logo_gfx = @$responseData["employeeInfo"]["companyLogo"];
                $employeeInfo->company          = @$responseData["employeeInfo"]["company"];
                $employeeInfo->save();

                $user->employee_info_id = $employeeInfo->id;
                $user->is_employee      = true;
                $user->save();
            }
        }

        return $user;
    }

    /**
     * @param string $field
     * @param string $value
     *
     * Randomized encryption support
     *
     * @return false|mixed
     */
    public static function getByField($field = "", $value = ""){
        if(!in_array($field, (new User())->encryptable)){
            /* @var User $user */
            $user = User::where($field, $value)->first();
            return $user;
        }

        $users = DB::table("users")->select(["id", $field])->whereNull("deleted_at")->get();
        foreach($users as $userRow){
            $fieldValue = "";
            try{
                $fieldValue = decrypt($userRow->{$field});
            }
            catch(\Exception $e){

            }
            if(strtolower(trim($fieldValue)) === strtolower(trim($value))){
                /* @var User $user */
                $user = User::where("id", $userRow->id)->first();
                return $user;
            }
        }
        return false;
    }

    /**
     * @param string $field
     * @param string $value
     *
     * Randomized encryption support
     *
     * @return false|mixed
     */
    public static function getAllByField($field = "", $value = ""){
        if(!in_array($field, (new User())->encryptable)){
            /* @var User $user */
            $user = User::where($field, $value)->get();
            return $user;
        }

        $outUsers = [];
        $users    = DB::table("users")->select(["id", $field])->whereNull("deleted_at")->get();
        foreach($users as $userRow){
            $fieldValue = "";
            try{
                $fieldValue = decrypt($userRow->{$field});
            }
            catch(\Exception $e){

            }
            if(strtolower(trim($fieldValue)) === strtolower(trim($value))){
                /* @var User $user */
                $outUsers[] = User::where("id", $userRow->id)->first();
            }
        }
        return $outUsers;
    }

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier(){
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims(){
        return [];
    }

    /**
     * @return HasOne
     */
    public function country(){
        return $this->hasOne('App\Http\Models\Api\Country', 'id', 'country_id');
    }

    /**
     * @return HasOne
     */
    public function employeeInfo(){
        return $this->hasOne('App\Http\Models\Api\EmployeeInfo', 'id', 'employee_info_id');
    }

    /**
     * @return HasMany
     */
    public function addresses(){
        return $this->hasMany('App\Http\Models\Api\Address', 'user_id', 'id');
    }

    /**
     * @return HasMany
     */
    public function purchases(){
        return $this->hasMany('App\Http\Models\Api\UserPurchase', 'user_id', 'id');
    }

    /**
     * @return HasMany
     */
    public function vouchers(){
        return $this->hasMany('App\Http\Models\Api\UserVoucher', 'member_id', 'member_id');
    }

    // -------- Api

    /**
     * @param SalesforceMarketingApi $salesforceMarketing
     * @param bool                   $all
     *
     * @throws IdentityProviderException
     */
    public static function apiFetchUserVouchers(SalesforceMarketingApi $salesforceMarketing, $all = false){
        // Find out database data age
        $latestChanged = date("Y-m-d H:i:s", Carbon::now()->subDays(7)->getTimestamp());
        if($all) $latestChanged = "2010-01-01 01:01:00";

        // Fetch all user vouchers from salesforce marketing
        $vouchers = $salesforceMarketing->getUserVouchersSince($latestChanged);
        $vouchers = fixVoucherData($vouchers);

        // Insert or update database
        with(new UserVoucher())->bulkInsertOrUpdate(with(new UserVoucher())->getVoucherDataAsDbFields($vouchers));
    }

    /**
     * @param SalesforceMarketingApi $salesforceMarketing
     * @param bool                   $all
     *
     * @throws IdentityProviderException
     */
    public static function apiFetchUserCampaigns(SalesforceMarketingApi $salesforceMarketing, $all = false){
        // Find out database data age
        $latestChanged = date("Y-m-d H:i:s", Carbon::now()->subDays(7)->getTimestamp());
        if($all) $latestChanged = "2010-01-01 01:01:00";

        // Fetch all user campaigns from salesforce marketing
        $vouchers = $salesforceMarketing->getUserVouchersSince($latestChanged, SalesforceMarketingApi::VOUCHER_CAMPAIGN);
        $vouchers = fixVoucherData($vouchers);

        // Insert or update database
        with(new UserVoucher())->bulkInsertOrUpdate(with(new UserVoucher())->getVoucherDataAsDbFields($vouchers));
    }

    /**
     * @param SalesforceMarketingApi $salesforceMarketing
     * @param bool                   $all
     *
     * @throws IdentityProviderException
     */
    public static function apiFetchUserPurchases(SalesforceMarketingApi $salesforceMarketing, $all = false){
        // Find out database data age
        $latestChanged = date("Y-m-d H:i:s", Carbon::now()->subDays(90)->getTimestamp());
        if($all) $latestChanged = "2010-01-01 01:01:00";

        // Fetch all user vouchers from salesforce marketing
        $purchases = $salesforceMarketing->getUserPurchasesSince($latestChanged);
        $purchases = with(new UserPurchase())->associateUserIds($purchases);

        // Insert or update database
        with(new UserPurchase())->bulkInsertOrUpdate(with(new UserPurchase())->getPurchaseDataAsDbFields($purchases));
    }

    /**
     * @param SalesforceMarketingApi $salesforceMarketing
     *
     * @throws IdentityProviderException
     */
    public static function apiFetchUserTitles(SalesforceMarketingApi $salesforceMarketing){
        // Fetch titles from salesforce
        $titles = $salesforceMarketing->getTitles();

        // Import prefix titles
        foreach($titles["prefix"] as $title){
            if(!($title["value"] ?? false)) continue;
            Title::updateOrCreate([
                "language_id" => app(RequestLanguage::class)->getActiveLanguageId(),
                "short_name"  => $title["value"],
            ], [
                "language_id" => app(RequestLanguage::class)->getActiveLanguageId(),
                "short_name"  => $title["value"],
                "full_name"   => $title["label"],
                "type"        => Title::TYPE_PREFIX,
                "status"      => Title::STATUS_ACTIVE,
            ]);
        }

        // Import postfix titles
        foreach($titles["postfix"] as $title){
            Title::updateOrCreate([
                "language_id" => app(RequestLanguage::class)->getActiveLanguageId(),
                "short_name"  => $title["value"],
            ], [
                "language_id" => app(RequestLanguage::class)->getActiveLanguageId(),
                "short_name"  => $title["value"],
                "full_name"   => $title["label"],
                "type"        => Title::TYPE_SUFFIX,
                "status"      => Title::STATUS_ACTIVE,
            ]);
        }
    }

    /**
     * @param SalesforceMarketingApi $salesforceMarketing
     *
     * @throws IdentityProviderException
     */
    public static function apiFetchCountries(SalesforceMarketingApi $salesforceMarketing){
        // Fetch countries from salesforce
        $countries = $salesforceMarketing->getCountries();
        $countries = $countries["values"] ?? [];

        // Import countries
        foreach($countries as $country){
            if(!isset($country["value"]) || !isset($country["label"])) continue;

            // Upsert language
            $language = Language::updateOrCreate(
                [
                    "code" => strtolower(trim($country["value"])),
                ],
                [
                    "code"    => strtolower(trim($country["value"])),
                    "name"    => trim($country["label"]),
                    "name_en" => strtotime(trim($country["label"])),
                ]
            );

            // Upsert country
            Country::updateOrCreate(
                [
                    "language_id"    => $language->id,
                    "translation_id" => app(RequestLanguage::class)->getActiveLanguageId(),
                ],
                [
                    "language_id"    => $language->id,
                    "translation_id" => app(RequestLanguage::class)->getActiveLanguageId(),
                    "name"           => $language->name,
                ]
            );
        }
    }

    /**
     * @param string $status
     *
     * @return float
     */
    public static function getNextThresholdByName($status = ""): float{
        $status = strtolower(trim($status));
        if($status === "silver") return static::GOLD_THRESHOLD;
        if($status === "gold") return static::PLATINUM_THRESHOLD;
        if($status === "platinum") return static::DIAMOND_THRESHOLD;
        if($status === "employee") return static::EMPLOYEE_THRESHOLD;
        return 0.0;
    }

    /**
     * @param string $status
     *
     * @return float
     */
    public static function getCurrentThresholdByName($status = ""): float{
        $statusAmount = 0.0;
        if(strtolower(trim($status)) === "silver") $statusAmount = static::SILVER_THRESHOLD;
        if(strtolower(trim($status)) === "gold") $statusAmount = static::GOLD_THRESHOLD;
        if(strtolower(trim($status)) === "platinum") $statusAmount = static::PLATINUM_THRESHOLD;
        if(strtolower(trim($status)) === "diamond") $statusAmount = static::DIAMOND_THRESHOLD;
        if(strtolower(trim($status)) === "diamond plus") $statusAmount = static::DIAMOND_THRESHOLD;
        if(strtolower(trim($status)) === "employee") $statusAmount = static::EMPLOYEE_THRESHOLD;
        return $statusAmount;
    }

    /**
     * @param string $status
     * @param float  $nextStatusAmount
     *
     * @return int
     */
    public static function calcNextStatusPercent($status = "", $nextStatusAmount = 0.0): int{
        if(!$status) return 0;
        $statusCurThreshold  = static::getCurrentThresholdByName($status);
        $statusNextThreshold = static::getNextThresholdByName($status);
        if($statusNextThreshold === 0) return 0;
        $neededToLevelUp = $statusNextThreshold - $statusCurThreshold;
        //$levelUpProgress = @(float)$nextStatusAmount - $statusCurThreshold;
        if($neededToLevelUp === 0.0) return 0;
        $progressPercent = (int)round($nextStatusAmount / $neededToLevelUp * 100.0);
        if($progressPercent < 0) return 0;
        if($progressPercent > 100) return 100;
        return $progressPercent;
    }
}
