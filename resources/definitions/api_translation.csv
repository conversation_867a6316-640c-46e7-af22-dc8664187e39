id;language_id;text_key;text_group;text_value
24;3;api:add_email.email.is_empty;2;Die angegebene E-Mail ist leer.
25;3;api:add_email.email.too_long;2;Die E-Mail sollte nicht l�nger als 128 Zeichen sein.
26;3;api:add_email.email.invalid_email_format;2;Das E-Mail-Format ist nicht korrekt.
27;3;api:add_email.email.email_already_used;2;Die E-Mail wurde bereits verwendet.
28;3;api:change_password.password.is_empty;2;<PERSON>te ausf�llen.
29;3;api:change_password.password.too_long;2;Das Passwort sollte nicht l�nger als 128 Zeichen sein.
30;3;api:change_password.password.too_short;2;Die L�nge des Passworts sollte mindestens 8 Zeichen betragen.
31;3;api:change_password.password.number_missing;2;Das Passwort muss mind. eine Zahl beinhalten.
32;3;api:change_password.password.letter_missing;2;Das Passwort muss mind. einen Buchstaben beinhalten.
33;3;api:change_password.password.uppercase_missing;2;Das Passwort muss mind. einen Gro�buchstaben beinhalten.
34;3;api:change_password.password.password_not_different;2;Das Passwort darf nicht ihr altes Passwort sein.
35;3;api:confirm_email.confirm_code.is_empty;2;Der �bergebene Wert ist leer.
36;3;api:confirm_email.confirm_code.not_found;2;Der Best�tigungscode konnte nicht gefunden werden.
37;3;api:confirm_email.confirm_code.code_expired;2;Der Best�tigungscode ist abgelaufen.
38;3;api:login.email.not_found;2;Die E-Mail-Adresse konnte nicht gefunden werden.
39;3;api:login.password.wrong_password;2;Das Passwort ist falsch.
40;3;api:login_via_member_post_code.member_id.not_found;2;Der Benutzer konnte nicht gefunden werden.
41;3;api:login_via_member_post_code.member_id.already_has_email;2;Dem Benutzer ist bereits eine verifizierte E-Mail-Adresse zugeordnet. Verwenden Sie stattdessen Passwort zur�cksetzen.
42;3;api:reset_password.email.is_empty;2;Die angegebene E-Mail ist leer.
43;3;api:reset_password.email.too_long;2;Die E-Mail sollte nicht l�nger als 128 Zeichen sein.
44;3;api:reset_password.email.invalid_email_format;2;Das E-Mail-Format ist nicht korrekt.
45;3;api:reset_password.email.email_not_found;2;Die E-Mail wurde nicht gefunden.
46;3;api:reset_password_confirm.email.is_empty;2;Die angegebene E-Mail ist leer.
47;3;api:reset_password_confirm.password.is_empty;2;Das angegebene Passwort ist leer.
48;3;api:reset_password_confirm.password.too_long;2;Das Kennwort sollte nicht l�nger als 128 Zeichen sein.
49;3;api:reset_password_confirm.password.too_short;2;Das Kennwort sollte l�nger als 8 Zeichen sein.
50;3;api:reset_password_confirm.password.number_missing;2;Das Kennwort sollte mindestens 1 Zahl enthalten.
51;3;api:reset_password_confirm.password.letter_missing;2;Das Kennwort sollte mindestens 1 Buchstaben enthalten.
52;3;api:reset_password_confirm.password.uppercase_missing;2;Das Kennwort sollte mindestens 1 Gro�buchstaben enthalten.
53;3;api:reset_password_confirm.email.not_found;2;Der Reset-Code konnte nicht gefunden werden.
54;3;api:reset_password_confirm.email.code_expired;2;Der Reset-Code ist abgelaufen.
55;3;api:validate_change_data.first_name.too_long;2;Der Vorname sollte nicht l�nger als 128 Zeichen sein.
56;3;api:validate_change_data.last_name.too_long;2;Der Nachname sollte nicht l�nger als 128 Zeichen sein.
57;3;api:validate_change_data.gender.invalid_value;2;"Es sind nur die folgenden Geschlechtswerte erlaubt: ""m|f|d""."
58;3;api:validate_change_data.title.too_long;2;Der Titel sollte nicht l�nger als 128 Zeichen sein.
59;3;api:validate_change_data.country_id.invalid_id;2;Die L�nderkennung ist ung�ltig.
60;3;api:validate_change_data.day_of_birth.invalid_format;2;Das Geburtsdatum muss das folgende Format haben: J-m-d Beispiel: 2020-09-20
61;3;api:validate_change_data.day_of_birth.invalid_value;2;Das eingegebene Geburtsdatum ist nicht g�ltig.
62;3;api:validate_change_data.day_of_birth.too_young;2;Der Benutzer muss mindestens 18 Jahre alt sein.
63;3;api:validate_change_data.mobile.too_long;2;Die Handynummer sollte nicht l�nger als 128 Zeichen sein.
64;3;api:validate_change_data.newsletter_sub.invalid_value;2;"Es sind nur die folgenden Newsletter-Verwendungswerte erlaubt: ""y|n""."
65;3;api:validate_change_data.email.too_long;2;Die E-Mail sollte nicht l�nger als 128 Zeichen sein.
66;3;api:validate_change_data.email.invalid_email_format;2;Das E-Mail-Format ist nicht korrekt.
67;3;api:validate_change_data.email.email_domain_blocked;2;Die E-Mail-Dom�ne ist gesperrt.
68;3;api:validate_change_data.email.email_already_used;2;Die E-Mail wurde bereits verwendet.
69;3;api:validate_change_data.street.too_long;2;Die Stra�e sollte nicht l�nger als 128 Zeichen sein.
70;3;api:validate_change_data.street_number.too_long;2;Die Stra�ennummer sollte nicht l�nger als 128 Zeichen sein.
71;3;api:validate_change_data.street_additional.too_long;2;Der Stra�enzusatz sollte nicht l�nger als 128 Zeichen sein.
72;3;api:validate_change_data.post_code.too_long;2;Die Postleitzahl sollte nicht l�nger als 128 Zeichen sein.
73;3;api:validate_change_data.city.too_long;2;Der Ort sollte nicht l�nger als 128 Zeichen sein.
74;3;api:validate_feedback.first_name.empty;2;Der Vorname ist leer.
75;3;api:validate_feedback.first_name.too_long;2;Der Vorname sollte nicht l�nger als 128 Zeichen sein.
76;3;api:validate_feedback.last_name.empty;2;Der Nachname ist leer.
77;3;api:validate_feedback.last_name.too_long;2;Der Nachname sollte nicht l�nger als 128 Zeichen sein.
78;3;api:validate_feedback.email.empty;2;Die E-Mail ist leer.
79;3;api:validate_feedback.email.too_long;2;Die E-Mail sollte nicht l�nger als 128 Zeichen sein.
80;3;api:validate_feedback.email.invalid_email_format;2;Das E-Mail-Format ist nicht korrekt.
81;3;api:validate_feedback.email.email_domain_blocked;2;Die E-Mail-Dom�ne ist blockiert.
82;3;api:validate_feedback.content.empty;2;Der Inhalt ist leer.
83;3;api:validate_feedback.content.too_long;2;Der Inhalt sollte nicht l�nger als 5000 Zeichen sein.
84;3;api:validate_login.email.is_empty;2;Die angegebene E-Mail ist leer.
85;3;api:validate_login.email.too_long;2;Die E-Mail sollte nicht l�nger als 128 Zeichen sein.
86;3;api:validate_login.email.invalid_email_format;2;Das E-Mail-Format ist nicht korrekt.
87;3;api:validate_login.password.is_empty;2;Das angegebene Passwort ist leer.
88;3;api:validate_login_old.member_id.is_empty;2;Die angegebene Mitglieds-ID ist leer.
89;3;api:validate_login_old.member_id.too_long;2;Die Mitgliedsnummer sollte nicht l�nger als 15 Zeichen sein.
90;3;api:validate_login_old.post_code.is_empty;2;Die angegebene Postleitzahl ist leer.
91;3;api:validate_login_old.post_code.too_long;2;Die Postleitzahl sollte nicht l�nger als 20 Zeichen sein.
92;3;api:validate_register.first_name.is_empty;2;Der angegebene Vorname ist leer.
93;3;api:validate_register.first_name.too_long;2;Der Vorname sollte nicht l�nger als 128 Zeichen sein.
94;3;api:validate_register.last_name.is_empty;2;Der �bergebene Nachname ist leer.
95;3;api:validate_register.last_name.too_long;2;Der Nachname sollte nicht l�nger als 128 Zeichen sein.
96;3;api:validate_register.gender.is_empty;2;Das angegebene Geschlecht ist leer.
97;3;api:validate_register.gender.invalid_value;2;"Es sind nur die folgenden Geschlechtswerte zul�ssig: ""m|f|d""."
98;3;api:validate_register.title.is_empty;2;Der Titel sollte nicht l�nger als 128 Zeichen sein.
99;3;api:validate_register.country_id.invalid_id;2;Die L�nderkennung ist ung�ltig.
100;3;api:validate_register.country_id.is_empty;2;Die angegebene L�nderkennung ist leer.
101;3;api:validate_register.day_of_birth.is_empty;2;Das angegebene Geburtsdatum ist leer.
102;3;api:validate_register.day_of_birth.invalid_format;2;Das Geburtsdatum muss das folgende Format haben: J-m-d Beispiel: 2020-09-20
103;3;api:validate_register.day_of_birth.invalid_value;2;Das eingegebene Geburtsdatum ist nicht g�ltig.
104;3;api:validate_register.day_of_birth.too_young;2;Der Benutzer muss mindestens 18 Jahre alt sein.
105;3;api:validate_register.mobile.too_long;2;Die Handynummer sollte nicht l�nger als 128 Zeichen sein.
106;3;api:validate_register.newsletter_sub.is_empty;2;Die mitgelieferte Newsletter-Best�tigung ist leer.
107;3;api:validate_register.newsletter_sub.invalid_value;2;"Es sind nur die folgenden Werte f�r die Newsletter-Benutzung erlaubt: ""y|n""."
108;3;api:validate_register.email.is_empty;2;Die angegebene E-Mail ist leer.
109;3;api:validate_register.email.too_long;2;Die E-Mail sollte nicht l�nger als 128 Zeichen sein.
110;3;api:validate_register.email.invalid_email_format;2;Das E-Mail-Format ist nicht korrekt.
111;3;api:validate_register.email.email_domain_blocked;2;Die E-Mail-Dom�ne ist gesperrt.
112;3;api:validate_register.email.email_already_used;2;Die E-Mail wurde bereits verwendet.
113;3;api:validate_register.password.is_empty;2;Das angegebene Passwort ist leer.
114;3;api:validate_register.password.too_long;2;Das Passwort sollte nicht l�nger als 128 Zeichen sein.
115;3;api:validate_register.password.too_short;2;Das Passwort sollte l�nger als 8 Zeichen sein.
116;3;api:validate_register.password.number_missing;2;Das Passwort sollte mindestens 1 Zahl enthalten.
117;3;api:validate_register.password.letter_missing;2;Das Passwort sollte mindestens 1 Buchstaben enthalten.
118;3;api:validate_register.password.uppercase_missing;2;Das Kennwort sollte mindestens 1 Gro�buchstaben enthalten.
119;3;api:validate_register.street.is_empty;2;Die angegebene Stra�e ist leer.
120;3;api:validate_register.street.too_long;2;Die Stra�e sollte nicht l�nger als 128 Zeichen sein.
121;3;api:validate_register.street_number.is_empty;2;Die angegebene street_number ist leer.
122;3;api:validate_register.street_number.too_long;2;Hausnummer sollte nicht l�nger als 128 Zeichen sein.
123;3;api:validate_register.street_additional.too_long;2;TOP sollte nicht l�nger als 128 Zeichen sein.
124;3;api:validate_register.post_code.is_empty;2;Die angegebene PLZ ist leer.
125;3;api:validate_register.post_code.too_long;2;Die PLZ sollte nicht l�nger als 128 Zeichen sein.
126;3;api:validate_register.city.is_empty;2;Der angegebene Ort ist leer.
127;3;api:validate_register.city.too_long;2;Der Ort sollte nicht l�nger als 128 Zeichen sein.
128;3;api:check_email.email.is_empty;2;Die angegebene E-Mail ist leer.
129;3;api:check_email.email.too_long;2;Die E-Mail sollte nicht l�nger als 128 Zeichen sein.
130;3;api:check_email.email.invalid_email_format;2;Das E-Mail-Format ist nicht korrekt.
131;3;api:check_email.email.email_domain_blocked;2;Die E-Mail-Dom�ne ist blockiert.
132;3;api:check_email.email.email_already_used;2;Die E-Mail wurde bereits verwendet.
133;3;api:check_member_id.member_id.is_empty;2;Die Mitglieds-ID ist leer.
134;3;api:check_member_id.member_id.member_id_not_found;2;Die Mitglieds-ID wurde noch nicht verwendet.
